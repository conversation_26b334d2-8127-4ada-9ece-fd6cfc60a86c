import pandas as pd

# 读取CSV数据
df = pd.read_csv('terminology_data.csv')

# 创建Excel文件
with pd.ExcelWriter('汽车安全气囊发生器专业术语库.xlsx', engine='openpyxl') as writer:
    # 主术语库
    df.to_excel(writer, sheet_name='术语总览', index=False)
    
    # 按技术领域分类
    domains = df['技术领域'].unique()
    for domain in domains:
        domain_df = df[df['技术领域'] == domain]
        sheet_name = domain[:10]  # 限制工作表名称长度
        domain_df.to_excel(writer, sheet_name=sheet_name, index=False)

print("Excel文档创建完成：汽车安全气囊发生器专业术语库.xlsx")
