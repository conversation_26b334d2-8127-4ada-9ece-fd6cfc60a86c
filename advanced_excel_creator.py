import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.formatting.rule import ColorScaleRule, CellIsRule
from datetime import datetime

def create_advanced_terminology_excel():
    """创建高级格式化的专业术语库Excel文档"""
    
    # 读取数据
    df = pd.read_csv('complete_terminology.csv')
    
    # 创建Excel工作簿
    wb = openpyxl.Workbook()
    wb.remove(wb.active)  # 删除默认工作表
    
    # 1. 创建使用说明工作表
    create_instruction_sheet_advanced(wb, df)
    
    # 2. 创建术语总览工作表（带高级格式）
    create_overview_sheet_advanced(wb, df)
    
    # 3. 创建技术领域分类工作表
    create_domain_sheets_advanced(wb, df)
    
    # 4. 创建标准对照表
    create_standards_sheet_advanced(wb)
    
    # 5. 创建产品系列码表
    create_product_series_sheet_advanced(wb)
    
    # 6. 创建术语索引表
    create_index_sheet_advanced(wb, df)
    
    # 7. 创建统计分析表
    create_statistics_sheet_advanced(wb, df)
    
    # 8. 创建查找功能表
    create_search_sheet_advanced(wb, df)
    
    # 保存文件
    wb.save('汽车安全气囊发生器专业术语库_高级版.xlsx')
    print("高级版Excel文档创建完成：汽车安全气囊发生器专业术语库_高级版.xlsx")
    print(f"包含 {len(df)} 个术语，{len(df['技术领域'].unique())} 个技术领域")

def create_instruction_sheet_advanced(wb, df):
    """创建高级使用说明工作表"""
    ws = wb.create_sheet("📋 使用说明", 0)
    
    # 标题
    ws['A1'] = "汽车安全气囊发生器专业术语库"
    ws['A1'].font = Font(size=20, bold=True, color="2F5597")
    ws['A1'].alignment = Alignment(horizontal="center", vertical="center")
    ws.merge_cells('A1:F1')
    
    # 版本信息区域
    ws['A3'] = "版本信息"
    ws['A3'].font = Font(size=14, bold=True, color="FFFFFF")
    ws['A3'].fill = PatternFill(start_color="2F5597", end_color="2F5597", fill_type="solid")
    ws.merge_cells('A3:B3')
    
    version_info = [
        ("版本号", "V1.0"),
        ("创建日期", datetime.now().strftime("%Y-%m-%d")),
        ("基于标准", "USCAR-24-2, AKLV-03, CFR-49 Section 178.65"),
        ("数据来源", "HY Files技术规范文档库"),
        ("术语总数", str(len(df))),
        ("技术领域数", str(len(df['技术领域'].unique()))),
        ("产品系列数", str(len(df['产品系列'].unique())))
    ]
    
    for i, (key, value) in enumerate(version_info, 4):
        ws[f'A{i}'] = key
        ws[f'B{i}'] = value
        ws[f'A{i}'].font = Font(bold=True)
    
    # 工作表说明
    ws['A12'] = "工作表说明"
    ws['A12'].font = Font(size=14, bold=True, color="FFFFFF")
    ws['A12'].fill = PatternFill(start_color="2F5597", end_color="2F5597", fill_type="solid")
    ws.merge_cells('A12:C12')
    
    sheet_descriptions = [
        ("�� 术语总览", "所有术语的汇总视图，支持筛选和排序", "主要工作表"),
        ("⚙️ 技术术语", "按技术领域分类的核心术语", "技术参考"),
        ("🔧 组件术语", "产品组件专用术语", "组件参考"),
        ("📚 标准对照表", "国际标准与HY规范对照", "标准参考"),
        ("🏷️ 产品系列码", "产品系列和代码对照", "产品参考"),
        ("🔎 术语索引", "快速查找索引表", "查找工具"),
        ("📊 统计分析", "术语使用频率和分布统计", "数据分析"),
        ("🔍 查找功能", "高级查找和筛选功能", "查找工具")
    ]
    
    for i, (sheet, desc, category) in enumerate(sheet_descriptions, 13):
        ws[f'A{i}'] = sheet
        ws[f'B{i}'] = desc
        ws[f'C{i}'] = category
        ws[f'A{i}'].font = Font(bold=True)
    
    # 使用说明
    ws['A22'] = "使用说明"
    ws['A22'].font = Font(size=14, bold=True, color="FFFFFF")
    ws['A22'].fill = PatternFill(start_color="2F5597", end_color="2F5597", fill_type="solid")
    ws.merge_cells('A22:C22')
    
    usage_notes = [
        "1. 术语等级：⭐⭐⭐(核心) ⭐⭐(重要) ⭐(一般)",
        "2. 组件等级：🔴(一级) 🟡(二级) 🟢(三级)",
        "3. 使用筛选功能快速查找特定术语",
        "4. 点击列标题可进行排序",
        "5. 查找功能表提供高级搜索选项",
        "6. 建议定期更新以反映标准变化",
        "7. 所有数据基于HY Files技术规范文档库"
    ]
    
    for i, note in enumerate(usage_notes, 23):
        ws[f'A{i}'] = note
    
    # 设置列宽
    ws.column_dimensions['A'].width = 25
    ws.column_dimensions['B'].width = 50
    ws.column_dimensions['C'].width = 20

def create_overview_sheet_advanced(wb, df):
    """创建高级格式化的术语总览工作表"""
    ws = wb.create_sheet("�� 术语总览")
    
    # 添加数据
    for r in dataframe_to_rows(df, index=False, header=True):
        ws.append(r)
    
    # 设置标题样式
    header_font = Font(bold=True, color="FFFFFF", size=11)
    header_fill = PatternFill(start_color="2F5597", end_color="2F5597", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
    
    for cell in ws[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # 设置列宽
    column_widths = [8, 25, 35, 12, 20, 45, 18, 25, 30, 15, 12, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[openpyxl.utils.get_column_letter(i)].width = width
    
    # 添加数据验证
    add_data_validation(ws, df)
    
    # 添加条件格式
    add_conditional_formatting(ws, df)
    
    # 添加筛选和冻结
    ws.auto_filter.ref = ws.dimensions
    ws.freeze_panes = "A2"
    
    # 添加边框
    add_borders(ws)

def add_data_validation(ws, df):
    """添加数据验证规则"""
    # 术语等级验证
    level_validation = DataValidation(
        type="list",
        formula1='"⭐⭐⭐,⭐⭐,⭐"',
        allow_blank=False
    )
    level_validation.error = "请选择有效的术语等级"
    level_validation.errorTitle = "无效输入"
    ws.add_data_validation(level_validation)
    level_validation.add(f"D2:D{len(df)+1}")
    
    # 技术领域验证
    domains = ','.join(df['技术领域'].unique())
    domain_validation = DataValidation(
        type="list",
        formula1=f'"{domains}"',
        allow_blank=False
    )
    domain_validation.error = "请选择有效的技术领域"
    domain_validation.errorTitle = "无效输入"
    ws.add_data_validation(domain_validation)
    domain_validation.add(f"E2:E{len(df)+1}")

def add_conditional_formatting(ws, df):
    """添加条件格式"""
    # 术语等级颜色编码
    high_priority_rule = CellIsRule(
        operator='equal',
        formula=['"⭐⭐⭐"'],
        fill=PatternFill(start_color="FFE6E6", end_color="FFE6E6", fill_type="solid")
    )
    ws.conditional_formatting.add(f"D2:D{len(df)+1}", high_priority_rule)
    
    medium_priority_rule = CellIsRule(
        operator='equal',
        formula=['"⭐⭐"'],
        fill=PatternFill(start_color="FFF2E6", end_color="FFF2E6", fill_type="solid")
    )
    ws.conditional_formatting.add(f"D2:D{len(df)+1}", medium_priority_rule)
    
    # 使用频率颜色编码
    frequency_rule = ColorScaleRule(
        start_type='min',
        start_color='FFFFFF',
        end_type='max',
        end_color='2F5597'
    )
    ws.conditional_formatting.add(f"K2:K{len(df)+1}", frequency_rule)

def add_borders(ws):
    """添加边框"""
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    for row in ws.iter_rows():
        for cell in row:
            cell.border = thin_border

def create_domain_sheets_advanced(wb, df):
    """创建技术领域分类工作表"""
    domains = df['技术领域'].unique()
    
    for domain in domains:
        domain_df = df[df['技术领域'] == domain]
        sheet_name = domain.replace('与', '&')[:10]
        
        ws = wb.create_sheet(sheet_name)
        
        # 添加数据
        for r in dataframe_to_rows(domain_df, index=False, header=True):
            ws.append(r)
        
        # 应用标准格式
        apply_standard_formatting(ws, domain_df)

def create_standards_sheet_advanced(wb):
    """创建标准对照表工作表"""
    ws = wb.create_sheet("📚 标准对照表")
    
    standards_data = [
        ["标准代码", "标准名称", "发布机构", "适用范围", "对应HY文档", "重要性", "备注"],
        ["USCAR-24-2", "汽车气囊发生器标准", "USCAR", "气囊发生器设计、测试、认证", "多个HY文档", "⭐⭐⭐", "主要参考标准"],
        ["AKLV-03", "欧洲汽车安全标准", "欧盟", "欧洲市场气囊发生器要求", "质量控制文档", "⭐⭐⭐", "欧洲认证标准"],
        ["CFR-49 Section 178.65", "DOT 39压力容器规范", "美国运输部", "压力容器设计制造", "CFR-49文件", "⭐⭐⭐", "压力容器法规"],
        ["SAE J403", "钢材化学成分标准", "SAE", "汽车用钢材规范", "HY03058", "⭐⭐", "材料标准"],
        ["AWS标准", "焊接工艺标准", "美国焊接学会", "焊接工艺和质量", "HY03051等", "⭐⭐⭐", "焊接技术标准"],
        ["ASTM标准", "材料测试标准", "ASTM", "材料性能测试", "材料规范文档", "⭐⭐", "材料测试标准"],
        ["ASME标准", "机械工程标准", "ASME", "压力容器和检测", "HY03051", "⭐⭐", "机械工程标准"],
        ["OSHA标准", "职业安全健康标准", "OSHA", "化学品安全管理", "MSDS文件", "⭐⭐⭐", "安全管理标准"],
        ["ISO标准", "国际标准化组织标准", "ISO", "质量管理体系", "质量管理文档", "⭐⭐", "质量管理标准"]
    ]
    
    for row in standards_data:
        ws.append(row)
    
    # 应用格式
    apply_standard_formatting(ws, None)

def create_product_series_sheet_advanced(wb):
    """创建产品系列码工作表"""
    ws = wb.create_sheet("🏷️ 产品系列码")
    
    series_data = [
        ["产品系列", "英文代码", "中文名称", "产品类型", "主要特征", "相关HY文档", "开发状态", "市场地位"],
        ["CADH", "CADH Series", "圆柱形驾驶员侧发生器", "驾驶员侧", "圆柱形结构，双级传爆", "HY03065, HY03075", "生产中", "成熟产品"],
        ["PH7", "PH7 Series", "饼状发生器第7代", "乘客侧", "饼状结构，大容量", "HY03183, HY03184", "生产中", "主力产品"],
        ["AHS", "AHS Series", "混合式发生器", "多用途", "混合气体，可调节", "HY03051, HY03044", "生产中", "通用产品"],
        ["G2P", "G2P Series", "新一代发生器", "新技术", "球形电阻焊，高效率", "HY03232", "开发中", "新技术产品"],
        ["G3P", "G3P Series", "第三代新发生器", "新技术", "进一步优化设计", "HY03252", "规划中", "未来产品"],
        ["SH5", "SH5 Series", "侧气囊发生器", "侧气囊", "小型化设计", "HY03051, HY03044", "生产中", "专用产品"],
        ["CH5", "CH5 Series", "帘式气囊发生器", "帘式气囊", "长条形结构", "HY03051, HY03044", "生产中", "专用产品"],
        ["PH6", "PH6 Series", "饼状发生器第6代", "乘客侧", "前代产品", "HY03175, HY03177", "停产", "历史产品"],
        ["APH", "APH Series", "亚太地区专用", "区域定制", "中国市场专用", "HY03163, HY03165", "生产中", "区域产品"]
    ]
    
    for row in series_data:
        ws.append(row)
    
    # 应用格式
    apply_standard_formatting(ws, None)

def create_index_sheet_advanced(wb, df):
    """创建术语索引工作表"""
    ws = wb.create_sheet("🔎 术语索引")
    
    # 创建索引数据
    index_data = [["索引字母", "中文术语", "英文术语", "所在行号", "技术领域", "术语等级"]]
    
    for idx, row in df.iterrows():
        first_letter = row['英文术语'][0].upper() if row['英文术语'] else 'Z'
        index_data.append([first_letter, row['中文术语'], row['英文术语'], idx+2, row['技术领域'], row['术语等级']])
    
    # 按首字母排序
    index_data[1:] = sorted(index_data[1:], key=lambda x: (x[0], x[1]))
    
    for row in index_data:
        ws.append(row)
    
    # 应用格式
    apply_standard_formatting(ws, None)

def create_statistics_sheet_advanced(wb, df):
    """创建统计分析工作表"""
    ws = wb.create_sheet("📊 统计分析")
    
    # 基本统计
    ws['A1'] = "术语库统计分析报告"
    ws['A1'].font = Font(size=16, bold=True, color="2F5597")
    ws.merge_cells('A1:D1')
    
    # 基本数据
    basic_stats = [
        ["统计项目", "数值", "说明"],
        ["术语总数", len(df), "包含所有技术领域的术语"],
        ["技术领域数", len(df['技术领域'].unique()), "涵盖的主要技术领域"],
        ["产品系列数", len(df['产品系列'].unique()), "支持的产品系列"],
        ["标准来源数", len(df['标准来源'].unique()), "引用的标准数量"]
    ]
    
    row = 3
    for stat in basic_stats:
        for col, value in enumerate(stat, 1):
            ws.cell(row=row, column=col, value=value)
        row += 1
    
    # 按技术领域分布
    ws['A8'] = "按技术领域分布"
    ws['A8'].font = Font(size=14, bold=True)
    
    domain_stats = df['技术领域'].value_counts()
    row = 9
    ws.cell(row=row, column=1, value="技术领域")
    ws.cell(row=row, column=2, value="术语数量")
    ws.cell(row=row, column=3, value="占比")
    row += 1
    
    for domain, count in domain_stats.items():
        ws.cell(row=row, column=1, value=domain)
        ws.cell(row=row, column=2, value=count)
        ws.cell(row=row, column=3, value=f"{count/len(df)*100:.1f}%")
        row += 1
    
    # 应用格式
    apply_standard_formatting(ws, None)

def create_search_sheet_advanced(wb, df):
    """创建查找功能工作表"""
    ws = wb.create_sheet("🔍 查找功能")
    
    # 查找说明
    ws['A1'] = "高级查找功能"
    ws['A1'].font = Font(size=16, bold=True, color="2F5597")
    ws.merge_cells('A1:D1')
    
    # 查找选项
    search_options = [
        ["查找类型", "查找范围", "使用方法", "示例"],
        ["按中文术语", "中文术语列", "输入完整或部分中文术语", "气体发生器"],
        ["按英文术语", "英文术语列", "输入完整或部分英文术语", "Gas Generator"],
        ["按技术领域", "技术领域列", "选择特定技术领域", "火工品技术"],
        ["按产品系列", "产品系列列", "选择特定产品系列", "CADH"],
        ["按术语等级", "术语等级列", "选择术语重要性等级", "⭐⭐⭐"],
        ["按标准来源", "标准来源列", "选择特定标准", "USCAR-24-2"],
        ["按使用频率", "使用频率列", "选择使用频率", "极高"]
    ]
    
    row = 3
    for option in search_options:
        for col, value in enumerate(option, 1):
            ws.cell(row=row, column=col, value=value)
        row += 1
    
    # 快速查找区域
    ws['A12'] = "快速查找区域"
    ws['A12'].font = Font(size=14, bold=True)
    
    # 创建下拉列表
    ws['A13'] = "技术领域："
    ws['B13'] = "请选择"
    
    ws['A14'] = "产品系列："
    ws['B14'] = "请选择"
    
    ws['A15'] = "术语等级："
    ws['B15'] = "请选择"
    
    # 应用格式
    apply_standard_formatting(ws, None)

def apply_standard_formatting(ws, df):
    """应用标准格式"""
    # 设置标题样式
    if ws.max_row > 0:
        header_font = Font(bold=True, color="FFFFFF", size=11)
        header_fill = PatternFill(start_color="2F5597", end_color="2F5597", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
        
        for cell in ws[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
    
    # 设置列宽
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # 添加筛选和冻结
    if ws.max_row > 1:
        ws.auto_filter.ref = ws.dimensions
        ws.freeze_panes = "A2"
    
    # 添加边框
    add_borders(ws)

if __name__ == "__main__":
    create_advanced_terminology_excel()
