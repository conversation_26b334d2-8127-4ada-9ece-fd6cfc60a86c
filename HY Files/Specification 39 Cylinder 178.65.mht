From: <Saved by Windows Internet Explorer 7>
Subject: Part 178: Specifications for packagings - Federal Motor Carrier Safety Administration
Date: Thu, 15 Oct 2009 17:04:35 -0400
MIME-Version: 1.0
Content-Type: multipart/related;
	type="text/html";
	boundary="----=_NextPart_000_0000_01CA4DB9.92AFE550"
X-MimeOLE: Produced By Microsoft MimeOLE V6.00.2900.3198

This is a multi-part message in MIME format.

------=_NextPart_000_0000_01CA4DB9.92AFE550
Content-Type: text/html;
	charset="utf-8"
Content-Transfer-Encoding: quoted-printable
Content-Location: http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/fmcsrruletext.aspx?chunkKey=090163348004773e&keyword=DOT39 CYLINDER

=EF=BB=BF<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" =
"http://www.w3c.org/TR/1999/REC-html401-19991224/loose.dtd">
<HTML><HEAD><TITLE>Part 178: Specifications for packagings - Federal =
Motor Carrier Safety Administration</TITLE>
<META http-equiv=3DContent-Type content=3D"text/html; =
charset=3Dutf-8"><LINK=20
href=3D"http://www.fmcsa.dot.gov/Style/style.css" type=3Dtext/css =
rel=3Dstylesheet>
<SCRIPT language=3DJavaScript =
src=3D"http://www.fmcsa.dot.gov/include/javascript.js"=20
type=3Dtext/javascript></SCRIPT>
<!--forsee survey code begins -->
<SCRIPT language=3DJavaScript=20
src=3D"http://www.fmcsa.dot.gov/foresee/foresee-trigger.js"=20
type=3Dtext/javascript></SCRIPT>
<!--forsee survey code ends --><!-- START OF SDC Advanced Tracking Code =
--><!-- Copyright (c) 1996-2007 WebTrends Inc.  All rights reserved. =
--><!-- V8.0 --><!-- $DateTime: 2007/02/16 11:44:56 $ -->
<SCRIPT language=3DJavaScript =
src=3D"http://www.fmcsa.dot.gov/include/Webtrends.js"=20
type=3Dtext/javascript></SCRIPT>
<!-- END OF SDC Advanced Tracking Code --><!--Gov Delivery popup code =
begins -->
<SCRIPT language=3DJavaScript=20
src=3D"http://www.fmcsa.dot.gov/include/GovDelivery.js"=20
type=3Dtext/javascript></SCRIPT>
<!--Gov Delivery popup code ends -->
<SCRIPT language=3Djavascript type=3Dtext/javascript>
      =20
         function fixTD() {
             //  debugger;
             //    alert("onload event");
             if (document.getElementById("TOCTop") !=3D null) {
                 var theight =3D =
document.getElementById("TOCTop").offsetHeight;
                 //  document.getElementById("TableCell6").style.height =
=3D theight - document.getElementById("TableCell6").offsetTop + 10;
                 document.getElementById("TableCell6").height =3D =
theight - document.getElementById("TableCell6").offsetTop + 10;
             }
//             if (screen.width >=3D 1024) {
//                 document.getElementById("Table1").width =3D "1000";
//             }
//             else {
//                 document.getElementById("Table1").width =3D "760"
//             }
         }
</SCRIPT>

<META content=3D"MSHTML 6.00.6000.16735" name=3DGENERATOR></HEAD>
<BODY onload=3Djavascript:fixTD()><NOSCRIPT>You must enable scripting on =
your=20
browser to be able to use all the functionality of the site - print this =
page=20
</NOSCRIPT>
<FORM id=3DaspnetForm name=3DaspnetForm=20
action=3Dfmcsrruletext.aspx?chunkKey=3D090163348004773e&amp;keyword=3DDOT=
39+CYLINDER=20
method=3Dpost>
<DIV><INPUT id=3D__VIEWSTATE type=3Dhidden=20
value=3D/wEPDwULLTE5NjE5MTQ0ODgPZBYCZg9kFgJmD2QWAgIBD2QWAgIBDxYCHgRUZXh0B=
VVQYXJ0IDE3ODogU3BlY2lmaWNhdGlvbnMgZm9yIHBhY2thZ2luZ3MgLSBGZWRlcmFsIE1vdG=
9yIENhcnJpZXIgU2FmZXR5IEFkbWluaXN0cmF0aW9uZGQgpBi21CyZenuYYOn4iHmIy4stuQ=3D=
=3D=20
name=3D__VIEWSTATE> </DIV><A name=3Dtop></A>
<DIV id=3Ddiv000001>
<TABLE id=3DTable1 width=3D760 align=3Dcenter border=3D0><!-- start of =
big table -->
  <TBODY>
  <TR>
    <TD><!-- TABLE 1 START -->
      <TABLE id=3DtblBanner=20
      style=3D"BORDER-RIGHT: #555 1px solid; BORDER-TOP: #555 1px solid; =
BORDER-LEFT: #555 1px solid"=20
      cellSpacing=3D0 cellPadding=3D0 width=3D"100%" border=3D0>
        <TBODY>
        <TR class=3Dutl id=3DtrTop=20
        style=3D"BACKGROUND-IMAGE: url(/images/new_style/top_bg.gif); =
HEIGHT: 18px"=20
        bgColor=3D#eeeeee><!--td width=3D"33%"><a href=3D"/" =
class=3D"utl">FMCSA Home</a></td-->
          <TD vAlign=3Dcenter align=3Dleft width=3D"2%"><A=20
            =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?chunkKey=3D090163348004773e&amp;keyword=3DDOT39 =
CYLINDER#toc"><IMG=20
            height=3D1 alt=3D"Skip Navigation"=20
            src=3D"http://www.fmcsa.dot.gov/images/style/1-pixel.gif" =
width=3D1=20
            border=3D0></A><A=20
            =
href=3D"http://www.fmcsa.dot.gov/redirect.aspx?page=3Dhttp://www.dot.gov"=
><IMG=20
            height=3D15 alt=3D"Department of Transportation Logo"=20
            src=3D"http://www.fmcsa.dot.gov/images/dot.gif" width=3D15 =
border=3D0></A>=20
          </TD>
          <TD vAlign=3Dcenter align=3Dleft>&nbsp;<A class=3Dutl=20
            =
href=3D"http://www.fmcsa.dot.gov/redirect.aspx?page=3Dhttp://www.dot.gov"=
>U.S.=20
            Department of Transportation</A> </TD>
          <TD vAlign=3Dcenter align=3Dright><A class=3Dutl=20
            =
href=3D"http://www.fmcsa.dot.gov/keyword-links.htm">Keyword&nbsp;Links</A=
>=20
            | <A class=3Dutl=20
            =
href=3D"http://www.fmcsa.dot.gov/about/contact/who-to-contact/contactus.h=
tm">Contact&nbsp;Us</A>=20
            | <A class=3Dutl =
href=3D"http://www.fmcsa.dot.gov/spanish/">Espa=C3=B1ol</A> <!-- <span =
class=3D"utl">Language:</span> <select class=3D"utl_select" =
name=3D"lang" id=3D"selLan" onChange=3D"javascript:switch_lan();">
								<option selected value=3D"en">English</option>
								<option  value=3D"es">Espa&#241;ol</option>							=09
							</select> --></TD></TR>
        <TR class=3Dbanneroverrun>
          <TD width=3D"100%" colSpan=3D4>
            <TABLE id=3DTable3 cellSpacing=3D0 cellPadding=3D0 =
width=3D"100%"=20
              border=3D0><TBODY>
              <TR>
                <TD class=3Dbannerimage=20
                style=3D"BACKGROUND-IMAGE: =
url(/images/new_style/header_bg3.jpg)">
                  <P class=3Dhdr>Federal Motor Carrier Safety=20
              Administration</P></TD></TR></TBODY></TABLE></TD></TR>
        <TR align=3Dleft bgColor=3D#a0161d>
          <TD colSpan=3D4>
            <TABLE id=3DtblMainMenu cellSpacing=3D0 cellPadding=3D0 =
width=3D"100%"=20
            border=3D0>
              <TBODY>
              <TR>
                <TD class=3DHomeCell align=3Dmiddle><A class=3Dnavi=20
                  href=3D"http://www.fmcsa.dot.gov/">Home</A></TD>
                <TD class=3Dcell align=3Dmiddle><A =
class=3DnaviFirstHighlight=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/rules-regulations.htm"=
>Rules=20
                  &amp; Regulations</A></TD>
                <TD class=3Dcell align=3Dmiddle><A class=3Dnavi=20
                  =
href=3D"http://www.fmcsa.dot.gov/registration-licensing/registration-lice=
nsing.htm">Registration=20
                  &amp; Licensing</A></TD>
                <TD class=3Dcell align=3Dmiddle><A class=3Dnavi=20
                  =
href=3D"http://www.fmcsa.dot.gov/forms/forms.htm">Forms</A></TD>
                <TD class=3Dcell align=3Dmiddle><A class=3Dnavi=20
                  =
href=3D"http://www.fmcsa.dot.gov/safety-security/safety-security.htm">Saf=
ety=20
                  &amp; Security</A></TD>
                <TD class=3Dcell align=3Dmiddle><A class=3Dnavi=20
                  =
href=3D"http://www.fmcsa.dot.gov/facts-research/art.htm">Facts=20
                  &amp; Research</A></TD>
                <TD class=3Dcell align=3Dmiddle><A class=3Dnavi=20
                  =
href=3D"http://www.fmcsa.dot.gov/intl-programs/intl-programs.htm"></A></T=
D>
                <TD class=3Dcell align=3Dmiddle><A class=3Dnavi=20
                  =
href=3D"http://www.fmcsa.dot.gov/about/aboutus.htm">About=20
                  =
FMCSA</A></TD></TR></TBODY></TABLE></TD></TR></TBODY></TABLE><!-- TABLE =
1 END --><!--Head Ends Here--><!--Body Starts Here--><!-- place holder =
for the Vertical Menu and Content -->
      <SCRIPT language=3Djavascript type=3Dtext/javascript>
     function fixTD()
     {
         var toctop =3D =
document.getElementById("ctl00_ctl00_MainContentArea_TOCTop");
         if(toctop !=3Dnull)
         {
            var theight =3D toctop.offsetHeight;
            //document.getElementById("TableCell6").style.height =3D =
theight - document.getElementById("TableCell6").offsetTop + 10;
            document.getElementById("TableCell6").height =3D theight - =
document.getElementById("TableCell6").offsetTop + 10;
         }
    }
</SCRIPT>

      <TABLE class=3DcontentContainerbread cellSpacing=3D0 =
cellPadding=3D0=20
      width=3D"100%" border=3D0>
        <TBODY>
        <TR style=3D"HEIGHT: 20px">
          <TD class=3Dbreadcrumbs style=3D"PADDING-LEFT: 5px" =
vAlign=3Dcenter=20
          colSpan=3D3><SPAN =
id=3Dctl00_ctl00_MainContentArea_lblBreadCrumb><A=20
            class=3Dbreadcrumbs =
href=3D"http://www.fmcsa.dot.gov/">Home</A> &gt; <A=20
            class=3Dbreadcrumbs=20
            =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/rules-regulations.htm"=
>Rules=20
            &amp; Regulations</A> &gt; 178.65</SPAN> </TD>
          <TD align=3Dright>
            <DIV id=3Dsearchboxdiv style=3D"FLOAT: right; MARGIN-RIGHT: =
6px"><LABEL=20
            accessKey=3Ds=20
            =
for=3Dctl00_ctl00_MainContentArea_SearchedValue>&nbsp;</LABEL> <INPUT=20
            class=3DInput.Text =
id=3Dctl00_ctl00_MainContentArea_SearchedValue=20
            onblur=3D"DefaultText(this, event);"=20
            onfocus=3D"DefaultText(this, event);" value=3D"Search All =
FMCSA Sites"=20
            name=3Dctl00$ctl00$MainContentArea$SearchedValue> <INPUT =
class=3Dbutton id=3Dctl00_ctl00_MainContentArea_Button115 type=3Dsubmit =
value=3DGo name=3Dctl00$ctl00$MainContentArea$Button115>=20
            </DIV></TD></TR></TBODY></TABLE><!-- Table for the TOC =
Vertical Nav / Main Content -->
      <TABLE class=3DleftNavAndContenttable id=3DtblContentArea =
cellSpacing=3D0=20
      cellPadding=3D0 width=3D"100%" border=3D0>
        <TBODY>
        <TR><!--Table Of Content(Left Nav) Started-->
          <TD class=3DleftNavMain2 =
id=3Dctl00_ctl00_MainContentArea_TOCTop=20
          style=3D"BORDER-RIGHT: #798ebd 0px solid; BORDER-LEFT-WIDTH: =
0px"=20
          vAlign=3Dtop align=3Dleft width=3D170>
            <TABLE id=3DTOCTable style=3D"HEIGHT: auto" cellSpacing=3D0 =
cellPadding=3D0=20
            width=3D160 align=3Dright border=3D0>
              <TBODY>
              <TR style=3D"MARGIN-RIGHT: 0px">
                <TD class=3DTOCUnselectedTD height=3D12><A =
name=3Dtoc>&nbsp;</A>=20
              </TD></TR>
              <TR>
                <TD class=3DTOCUnselectedTD style=3D"PADDING-LEFT: 0px" =
vAlign=3Dtop=20
                align=3Dleft width=3D"100%">
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/rules-regulations.htm"=
>Overview</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCHeadTD>Federal Regulations</TD></TR>
              <TR style=3D"PADDING-RIGHT: 1px">
                <TD class=3DTOCSelectedTD><A=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrguide.aspx?section_type=3DA">All</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrguide.aspx?section_type=3DD">Driver</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrguide.aspx?section_type=3DV">Vehicle</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrguide.aspx?section_type=3DC">Company</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrguide.aspx?section_type=3DH">FMCSA=20
                  Hazmat</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrguide.aspx?section_type=3DG">Regulatory=20
                  Guidance</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCHeadTD>Rulemakings and Notices</TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/rulemak=
ings/rule-programs/new_rulemakings.aspx?cat=3Dfinal">Final=20
                  Rules</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/rulemak=
ings/rule-programs/new_rulemakings.aspx?cat=3Dinterim">Interim=20
                  Final Rules</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/rulemak=
ings/rule-programs/new_rulemakings.aspx?cat=3Dproposed">Proposed=20
                  Rules</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/rulemak=
ings/rule-programs/new_rulemakings.aspx?cat=3Dnotice">Notices</A></TD></T=
R>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCHeadTD>Topics of Interest</TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/topics/hos/index.htm">=
Hours=20
                  of Service (HOS)</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/safety-security/hazmat/hm-theme.htm">Haz=
ardous=20
                  Materials</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/topics/IEP/index.htm">=
Intermodal=20
                  Equipment Providers (IEP)</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/topics/medical/medical=
.htm">Medical=20
                  Program</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/topics/mep/mep.htm">Me=
dical=20
                  Expert Panels</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/topics/nafta/nafta.htm=
">NAFTA=20
                  Rules</A></TD></TR>
              <TR style=3D"MARGIN-RIGHT: 1px">
                <TD class=3DTOCUnselectedTD><A class=3Dheadtoc=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/topics/drug/drug.htm">=
Drug=20
                  &amp; Alcohol Testing</A></TD></TR></TD></TR>
              <TR>
                <TD class=3DTOCUnselectedTD id=3DTableCell6 =
height=3D12><A=20
                  name=3Dtoc>&nbsp;</A> =
</TD></TR></TBODY></TABLE></TD><!--Table Of Content(Left Nav) Ended-->
          <TD=20
          style=3D"WIDTH: 5px; WHITE-SPACE: nowrap; BACKGROUND-COLOR: =
#fff; BORDER-RIGHT-WIDTH: 1px">&nbsp;&nbsp;=20
          </TD><!--Body Contain Starts -->
          <TD class=3Dcontentarea vAlign=3Dtop colSpan=3D2 =
height=3D"100%">
            <TABLE id=3DTable9 style=3D"HEIGHT: 100%" cellSpacing=3D0 =
cellPadding=3D0=20
            width=3D"100%" border=3D0>
              <TBODY>
              <TR>
                <TD vAlign=3Dtop width=3D"100%" colSpan=3D2>
                  <TABLE id=3DTable11 cellSpacing=3D0 cellPadding=3D0 =
width=3D"100%"=20
                  border=3D0>
                    <TBODY>
                    <TR id=3DtrHead><!-- <td width=3D"524"> -->
                      <TD class=3Dtitleheadline vAlign=3Dbottom =
width=3D"100%"=20
                      colSpan=3D2>
                        <H2 class=3Dtitleheadline>Part 178: =
Specifications for=20
                        packagings </H2></TD></TR>
                    <TR id=3DtrPrintlink>
                      <TD align=3Dleft><SPAN=20
                        =
id=3Dctl00_ctl00_MainContentArea_spnLink></SPAN></TD>
                      <TD align=3Dright><A class=3Dbreadcrumbs=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsr_print_rule.aspx?chunkKey=3D090163348004773e&amp;keyword=3DDOT39%20CY=
LINDER"><IMG=20
                        alt=3D"Print this page"=20
                        =
src=3D"http://www.fmcsa.dot.gov/images/printerimage.gif"=20
                        border=3D0> Print</A> &nbsp; =
</TD></TR></TBODY></TABLE></TD></TR>
              <TR>
                <TD id=3Dcontent1 style=3D"PADDING-RIGHT: 4px" =
vAlign=3Dtop=20
                height=3D"100%"><!--  Added to have space before =
headings --><!--Content Starts --><!--PAGEWATCH CODE=3D""-->
                  <SCRIPT language=3Djavascript type=3Dtext/javascript>
    function searchPrompt(defaultText, treatAsPhrase, textColor, =
bgColor) {
        // This function prompts the user for any words that should
        // be highlighted on this web page
        if (!defaultText) {
            defaultText =3D "";
        }

        // we can optionally use our own highlight tag values
        if ((!textColor) || (!bgColor)) {
            highlightStartTag =3D "";
            highlightEndTag =3D "";
        } else {
            highlightStartTag =3D "<font style=3D'color:" + textColor + =
"; background-color:" + bgColor + ";'>";
            highlightEndTag =3D "</font>";
        }

        if (treatAsPhrase) {
            promptText =3D "Please enter the phrase you'd like to search =
for:";
        } else {
            promptText =3D "Please enter the words you'd like to search =
for, separated by spaces:";
        }
       // debugger;
        // searchText =3D prompt(promptText, defaultText);
        searchText =3D defaultText;

        if (!searchText) {
          //  alert("No search terms were entered. Exiting function.");
            return false;
        }

        return highlightSearchTerms(searchText, treatAsPhrase, true, =
highlightStartTag, highlightEndTag);
    }

    /*
    * This is sort of a wrapper function to the doHighlight function.
    * It takes the searchText that you pass, optionally splits it into
    * separate words, and transforms the text on the current web page.
    * Only the "searchText" parameter is required; all other parameters
    * are optional and can be omitted.
    */
    function highlightSearchTerms(searchText, treatAsPhrase, =
warnOnFailure, highlightStartTag, highlightEndTag) {
        // if the treatAsPhrase parameter is true, then we should search =
for=20
        // the entire phrase that was entered; otherwise, we will split =
the
        // search string so that each word is searched for and =
highlighted
        // individually
        if (treatAsPhrase) {
            searchArray =3D [searchText];
        } else {
            searchArray =3D searchText.split(" ");
        }

        if (!document.body || typeof (document.body.innerHTML) =3D=3D =
"undefined") {
            if (warnOnFailure) {
            //    alert("Sorry, for some reason the text of this page is =
unavailable. Searching will not work.");
            }
            return false;
        }
    //    debugger;
        var bodyText =
=3Ddocument.getElementById('ctl00_ctl00_MainContentArea_page_content_plac=
e_holder_pnlText').innerHTML;
        for (var i =3D 0; i < searchArray.length; i++) {
            bodyText =3D doHighlight(bodyText, searchArray[i], =
highlightStartTag, highlightEndTag);
        }

        =
document.getElementById('ctl00_ctl00_MainContentArea_page_content_place_h=
older_pnlText').innerHTML =3D bodyText;
        return true;
    }

    /*
    * This is the function that actually highlights a text string by
    * adding HTML tags before and after all occurrences of the search
    * term. You can pass your own tags if you'd like, or if the
    * highlightStartTag or highlightEndTag parameters are omitted or
    * are empty strings then the default <font> tags will be used.
    */
    function doHighlight(bodyText, searchTerm, highlightStartTag, =
highlightEndTag) {
        // the highlightStartTag and highlightEndTag parameters are =
optional
        if ((!highlightStartTag) || (!highlightEndTag)) {
            highlightStartTag =3D "<font style=3D'color:blue; =
background-color:yellow;'>";
            highlightEndTag =3D "</font>";
        }

        // find all occurences of the search term in the given text,
        // and add some "highlight" tags to them (we're not using a
        // regular expression search, because we want to filter out
        // matches that occur within HTML tags and script blocks, so
        // we have to do a little extra validation)
        var newText =3D "";
        var i =3D -1;
        var lcSearchTerm =3D searchTerm.toLowerCase();
        var lcBodyText =3D bodyText.toLowerCase();

        while (bodyText.length > 0) {
            i =3D lcBodyText.indexOf(lcSearchTerm, i + 1);
            if (i < 0) {
                newText +=3D bodyText;
                bodyText =3D "";
            } else {
                // skip anything inside an HTML tag
                if (bodyText.lastIndexOf(">", i) >=3D =
bodyText.lastIndexOf("<", i)) {
                    // skip anything inside a <script> block
                    if (lcBodyText.lastIndexOf("/script>", i) >=3D =
lcBodyText.lastIndexOf("<script", i)) {
                        newText +=3D bodyText.substring(0, i) + =
highlightStartTag + bodyText.substr(i, searchTerm.length) + =
highlightEndTag;
                        bodyText =3D bodyText.substr(i + =
searchTerm.length);
                        lcBodyText =3D bodyText.toLowerCase();
                        i =3D -1;
                    }
                }
            }
        }

        return newText;
    }



    function window.onload()
    {
        //  debugger;
        var keyword =3D =
document.getElementById('ctl00_ctl00_MainContentArea_page_content_place_h=
older_hdnKeyword').value;
        if (keyword !=3D "" || keyword !=3D " ")
        {
            searchPrompt(keyword, true, 'black', 'yellow');
        }
    }

</SCRIPT>

                  <TABLE cellSpacing=3D0 cellPadding=3D0 width=3D"98%" =
align=3Dcenter=20
                  border=3D0>
                    <TBODY>
                    <TR>
                      <TD>
                        <TABLE cellSpacing=3D1 cellPadding=3D5 =
width=3D"100%"=20
                        bgColor=3D#000000 border=3D0>
                          <TBODY>
                          <TR>
                            <TD align=3Dmiddle width=3D"100%" =
bgColor=3D#ececec>
                              <DIV=20
                              onkeypress=3D"javascript:return =
WebForm_FireDefaultButton(event, =
'ctl00_ctl00_MainContentArea_page_content_place_holder_RulesSearch1_btnGo=
')"=20
                              =
id=3Dctl00_ctl00_MainContentArea_page_content_place_holder_RulesSearch1_p=
nlSearch=20
                              style=3D"WIDTH: 100%" align=3Dcenter=20
                              =
bgcolor=3D"#ECECEC"><B>Search</B>&nbsp;&nbsp;=20
                              <SELECT=20
                              =
id=3Dctl00_ctl00_MainContentArea_page_content_place_holder_RulesSearch1_d=
dlRuletype=20
                              =
name=3Dctl00$ctl00$MainContentArea$page_content_place_holder$RulesSearch1=
$ddlRuletype>=20
                                <OPTION value=3DA selected>ALL=20
                                Regulations</OPTION> <OPTION =
value=3DD>Driver=20
                                Regulations</OPTION> <OPTION =
value=3DV>Vehicle=20
                                Regulations</OPTION> <OPTION =
value=3DC>Company=20
                                Regulations</OPTION> <OPTION =
value=3DH>FMCSA=20
                                Hazmat Regulations</OPTION> <OPTION=20
                                value=3DG>Regulatory =
Guidance</OPTION></SELECT>=20
                              &nbsp;&nbsp;for&nbsp;&nbsp;<INPUT=20
                              =
id=3Dctl00_ctl00_MainContentArea_page_content_place_holder_RulesSearch1_t=
xtSearch=20
                              style=3D"WIDTH: 120px"=20
                              =
name=3Dctl00$ctl00$MainContentArea$page_content_place_holder$RulesSearch1=
$txtSearch>&nbsp;&nbsp;=20
<INPUT class=3Dbutton =
id=3Dctl00_ctl00_MainContentArea_page_content_place_holder_RulesSearch1_b=
tnGo type=3Dsubmit value=3DGo =
name=3Dctl00$ctl00$MainContentArea$page_content_place_holder$RulesSearch1=
$btnGo><BR><FONT=20
                              size=3D-2>Examples: <I>Medical Form, =
391.53,=20
                              391</I></FONT> =
</DIV></TD></TR></TBODY></TABLE></TD></TR>
                    <TR>&nbsp;</TR>
                    <TR>
                      <TD><INPUT=20
                        =
id=3Dctl00_ctl00_MainContentArea_page_content_place_holder_hdnSumary=20
                        type=3Dhidden value=3D"     "=20
                        =
name=3Dctl00$ctl00$MainContentArea$page_content_place_holder$hdnSumary><I=
NPUT=20
                        =
id=3Dctl00_ctl00_MainContentArea_page_content_place_holder_hdnpresDescrip=
tion=20
                        type=3Dhidden value=3D"    "=20
                        =
name=3Dctl00$ctl00$MainContentArea$page_content_place_holder$hdnpresDescr=
iption>=20
                      </TD></TR>
                    <TR>
                      <TD vAlign=3Dtop width=3D"100%" colSpan=3D2><IMG =
alt=3DUp=20
                        =
src=3D"http://www.fmcsa.dot.gov/images/style/arrow_left_up.jpg">&nbsp;<A =

                        =
id=3Dctl00_ctl00_MainContentArea_page_content_place_holder_lnkAll=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrguide.aspx">All=20
                        Regulations</A> <BR>&nbsp; </TD></TR>
                    <TR>
                      <TD>&nbsp;</TD></TR>
                    <TR>
                      <TD width=3D"100%" colSpan=3D2>
                        <TABLE width=3D"100%">
                          <TBODY>
                          <TR>
                            <TD align=3Dleft width=3D"50%"></TD>
                            <TD align=3Dright=20
                    width=3D"50%"></TD></TR></TBODY></TABLE></TD></TR>
                    <TR>
                      <TD vAlign=3Dtop colSpan=3D2></TD></TR>
                    <TR>
                      <TD vAlign=3Dtop width=3D"100%" colSpan=3D2>
                        <TABLE class=3Drellink id=3DTable4=20
                        style=3D"MARGIN-TOP: 15px; FLOAT: right; =
MARGIN-LEFT: 3px"=20
                        cellSpacing=3D0 cellPadding=3D0 width=3D120 =
border=3D0>
                          <TBODY>
                          <TR>
                            <TD class=3Drlhdr>Related Links </TD></TR>
                          <TR align=3Dleft>
                            <TD align=3Dleft>
                              <UL>
                                <LI><A class=3Dnews=20
                                =
id=3Dctl00_ctl00_MainContentArea_page_content_place_holder_lnkDisclaimer =

                                =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/d=
isclaim.htm">Disclaimer</A>=20

                                <LI><A class=3Dnews=20
                                =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/h=
elp.asp">Help</A>=20
                                =
</LI></UL></TD></TR></TBODY></TABLE><INPUT=20
                        =
id=3Dctl00_ctl00_MainContentArea_page_content_place_holder_hdnKeyword=20
                        type=3Dhidden value=3D"DOT39 CYLINDER"=20
                        =
name=3Dctl00$ctl00$MainContentArea$page_content_place_holder$hdnKeyword> =


                        <DIV=20
                        =
id=3Dctl00_ctl00_MainContentArea_page_content_place_holder_pnlText><STRON=
G></STRONG><BR><BR><STRONG=20
                        xmlns:s=3D"http://states.data">=C2=A7178.65 =
Specification 39=20
                        non-reusable (non-refillable) =
cylinders.</STRONG> <A=20
                        id=3Dr49CFR178.65-a></A>
                        <P id=3Dr49CFR178.65-a>(a) <B=20
                        xmlns:s=3D"http://states.data">Type, size, =
service=20
                        pressure, and test pressure</B>. A DOT 39 =
cylinder is a=20
                        seamless, welded, or brazed cylinder with a =
service=20
                        pressure not to exceed 80 percent of the test =
pressure.=20
                        Spherical pressure vessels are authorized and =
covered by=20
                        references to cylinders in this =
specification.</P><A=20
                        id=3Dr49CFR178.65-a-1></A>
                        <P id=3Dr49CFR178.65-a-1>(a)(1) <I>Size =
limitation</I>.=20
                        Maximum water capacity may not exceed: (i) 55 =
pounds=20
                        (1,526 cubic inches) for a service pressure of =
500=20
                        p.s.i.g. or less, and (ii) 10 pounds (277 cubic =
inches)=20
                        for a service pressure in excess of 500 =
p.s.i.g.</P><A=20
                        id=3Dr49CFR178.65-a-2></A>
                        <P id=3Dr49CFR178.65-a-2>(a)(2) <I>Test =
pressure</I>. The=20
                        minimum test pressure is the maximum pressure of =

                        contents at 130 =C2=B0F or 180 p.s.i.g. =
whichever is=20
                        greater.</P><A id=3Dr49CFR178.65-a-3></A>
                        <P id=3Dr49CFR178.65-a-3>(a)(3) <I>Pressure of=20
                        contents</I>. The term =
=E2=80=98=E2=80=98pressure of contents=E2=80=99=E2=80=99 as used=20
                        in this specification means the total pressure =
of all=20
                        the materials to be shipped in the =
cylinder.</P><A=20
                        id=3Dr49CFR178.65-b></A>
                        <P id=3Dr49CFR178.65-b>(b) <B=20
                        xmlns:s=3D"http://states.data">Material; steel =
or=20
                        aluminum</B>. The cylinder must be constructed =
of either=20
                        steel or aluminum conforming to the following=20
                        requirements:</P><A id=3Dr49CFR178.65-b-1></A>
                        <P id=3Dr49CFR178.65-b-1>(b)(1) <B=20
                        xmlns:s=3D"http://states.data">Steel.</B><A=20
                        id=3Dr49CFR178.65-b-1-i></A> (i) The steel =
analysis must=20
                        conform to the following:
                        <TABLE class=3Dvr cellPadding=3D4>
                          <CAPTION =
xmlns:s=3D"http://states.data"><B>=C2=A7178.65 -=20
                          Table 1</B></CAPTION>
                          <COLGROUP xmlns:s=3D"http://states.data">
                          <COL width=3D"53%">
                          <COL width=3D"19%">
                          <COL width=3D"28%"></COLGROUP>
                          <THEAD xmlns:s=3D"http://states.data">
                          <TR vAlign=3Dtop>
                            <TH class=3Dvr vAlign=3Dbottom scope=3Dcol=20
                            align=3Dmiddle></TH>
                            <TH class=3Dvr vAlign=3Dbottom scope=3Dcol=20
                              align=3Dmiddle>Ladle analysis</TH>
                            <TH class=3Dvr=20
                            style=3D"BORDER-RIGHT-STYLE: none; =
BORDER-BOTTOM-STYLE: solid"=20
                            vAlign=3Dbottom scope=3Dcol =
align=3Dmiddle>Check=20
                            analysis</TH></TR></THEAD>
                          <TBODY xmlns:s=3D"http://states.data">
                          <TR vAlign=3Dtop>
                            <TD class=3Dvr vAlign=3Dtop scope=3Drow=20
                              align=3Dleft>Carbon, maximum percent</TD>
                            <TD class=3Dvr vAlign=3Dtop =
align=3Dmiddle>0.12</TD>
                            <TD class=3Dvr style=3D"BORDER-RIGHT-STYLE: =
none"=20
                            vAlign=3Dtop align=3Dmiddle>0.15</TD></TR>
                          <TR vAlign=3Dtop>
                            <TD class=3Dvr vAlign=3Dtop scope=3Drow=20
                              align=3Dleft>Phosphorus, maximum =
percent</TD>
                            <TD class=3Dvr vAlign=3Dtop =
align=3Dmiddle>.04</TD>
                            <TD class=3Dvr style=3D"BORDER-RIGHT-STYLE: =
none"=20
                            vAlign=3Dtop align=3Dmiddle>.05</TD></TR>
                          <TR vAlign=3Dtop>
                            <TD class=3Dvr style=3D"BORDER-BOTTOM-STYLE: =
solid"=20
                            vAlign=3Dtop scope=3Drow =
align=3Dleft>Sulfur, maximum=20
                              percent</TD>
                            <TD class=3Dvr style=3D"BORDER-BOTTOM-STYLE: =
solid"=20
                            vAlign=3Dtop align=3Dmiddle>.05</TD>
                            <TD class=3Dvr=20
                            style=3D"BORDER-RIGHT-STYLE: none; =
BORDER-BOTTOM-STYLE: solid"=20
                            vAlign=3Dtop=20
                        =
align=3Dmiddle>.06</TD></TR></TBODY></TABLE></P><A=20
                        id=3Dr49CFR178.65-b-1-ii></A>
                        <P id=3Dr49CFR178.65-b-1-ii>(b)(1)(ii) For a =
cylinder made=20
                        of seamless steel tubing with integrally formed =
ends,=20
                        hot drawn, and finished, content percent for the =

                        following may not exceed: Carbon, 0.55; =
phosphorous,=20
                        0.045; sulfur, 0.050.</P><A =
id=3Dr49CFR178.65-b-1-iii></A>
                        <P id=3Dr49CFR178.65-b-1-iii>(b)(1)(iii) For =
non-heat=20
                        treated welded steel cylinders, adequately =
killed deep=20
                        drawing quality steel is required.</P><A=20
                        id=3Dr49CFR178.65-b-1-iv></A>
                        <P id=3Dr49CFR178.65-b-1-iv>(b)(1)(iv) =
Longitudinal or=20
                        helical welded cylinders are not authorized for =
service=20
                        pressures in excess of 500 p.s.i.g.</P><A=20
                        id=3Dr49CFR178.65-b-2></A>
                        <P id=3Dr49CFR178.65-b-2>(b)(2) <I>Aluminum</I>. =
Aluminum=20
                        is not authorized for service pressures in =
excess of 500=20
                        psig. The analysis of the aluminum must conform =
to the=20
                        Aluminum Association standard for alloys 1060, =
1100,=20
                        1170, 3003, 5052, 5086, 5154, 6061, and 6063, as =

                        specified in its publication entitled =
=E2=80=9CAluminum=20
                        Standards and Data=E2=80=9D (IBR, see <A=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?reg=3Dr49CFR171.7">=C2=A7171.7</A>=20
                        of this subchapter).</P><A =
id=3Dr49CFR178.65-b-3></A>
                        <P id=3Dr49CFR178.65-b-3>(b)(3) Material with =
seams,=20
                        cracks, laminations, or other injurious defects =
not=20
                        permitted.</P><A id=3Dr49CFR178.65-b-4></A>
                        <P id=3Dr49CFR178.65-b-4>(b)(4) Material used =
must be=20
                        identified by any suitable method.</P><A=20
                        id=3Dr49CFR178.65-c></A>
                        <P id=3Dr49CFR178.65-c>(c) <B=20
                        xmlns:s=3D"http://states.data">Manufacture</B>. =
<A=20
                        id=3Dr49CFR178.65-c-1></A>(1) General =
manufacturing=20
                        requirements are as follows:</P><A=20
                        id=3Dr49CFR178.65-c-1-i></A>
                        <P id=3Dr49CFR178.65-c-1-i>(c)(1)(i) The surface =
finish=20
                        must be uniform and reasonably smooth.</P><A=20
                        id=3Dr49CFR178.65-c-1-ii></A>
                        <P id=3Dr49CFR178.65-c-1-ii>(c)(1)(ii) Inside =
surfaces=20
                        must be clean, dry, and free of loose =
particles.</P><A=20
                        id=3Dr49CFR178.65-c-1-iii></A>
                        <P id=3Dr49CFR178.65-c-1-iii>(c)(1)(iii) No =
defect of any=20
                        kind is permitted if it is likely to weaken a =
finished=20
                        cylinder.</P><A id=3Dr49CFR178.65-c-2></A>
                        <P id=3Dr49CFR178.65-c-2>(c)(2) Requirements for =

                        seams:</P><A id=3Dr49CFR178.65-c-2-i></A>
                        <P id=3Dr49CFR178.65-c-2-i>(c)(2)(i) Brazing is =
not=20
                        authorized on aluminum cylinders.</P><A=20
                        id=3Dr49CFR178.65-c-2-ii></A>
                        <P id=3Dr49CFR178.65-c-2-ii>(c)(2)(ii) Brazing =
material=20
                        must have a melting point of not lower than =
1,000=20
                        =C2=B0F.</P><A id=3Dr49CFR178.65-c-2-iii></A>
                        <P id=3Dr49CFR178.65-c-2-iii>(c)(2)(iii) Brazed =
seams must=20
                        be assembled with proper fit to ensure complete=20
                        penetration of the brazing material throughout =
the=20
                        brazed joint.</P><A =
id=3Dr49CFR178.65-c-2-iv></A>
                        <P id=3Dr49CFR178.65-c-2-iv>(c)(2)(iv) Minimum =
width of=20
                        brazed joints must be at least four times the =
thickness=20
                        of the shell wall.</P><A =
id=3Dr49CFR178.65-c-2-v></A>
                        <P id=3Dr49CFR178.65-c-2-v>(c)(2)(v) Brazed =
seams must=20
                        have design strength equal to or greater than =
1.5 times=20
                        the minimum strength of the shell wall.</P><A=20
                        id=3Dr49CFR178.65-c-2-vi></A>
                        <P id=3Dr49CFR178.65-c-2-vi>(c)(2)(vi) Welded =
seams must=20
                        be properly aligned and welded by a method that =
provides=20
                        clean, uniform joints with adequate =
penetration.</P><A=20
                        id=3Dr49CFR178.65-c-2-vii></A>
                        <P id=3Dr49CFR178.65-c-2-vii>(c)(2)(vii) Welded =
joints=20
                        must have a strength equal to or greater than =
the=20
                        minimum strength of the shell material in the =
finished=20
                        cylinder.</P><A id=3Dr49CFR178.65-c-3></A>
                        <P id=3Dr49CFR178.65-c-3>(c)(3) Attachments to =
the=20
                        cylinder are permitted by any means which will =
not be=20
                        detrimental to the integrity of the cylinder. =
Welding or=20
                        brazing of attachments to the cylinder must be =
completed=20
                        prior to all pressure tests.</P><A=20
                        id=3Dr49CFR178.65-c-4></A>
                        <P id=3Dr49CFR178.65-c-4>(c)(4) Welding =
procedures and=20
                        operators must be qualified in accordance with =
CGA=20
                        Pamphlet C-3 (IBR, see <A=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?reg=3Dr49CFR171.7">=C2=A7171.7</A>=20
                        of this subchapter).</P><A =
id=3Dr49CFR178.65-d></A>
                        <P id=3Dr49CFR178.65-d>(d) <B=20
                        xmlns:s=3D"http://states.data">Wall =
thickness</B>. The=20
                        minimum wall thickness must be such that the =
wall stress=20
                        at test pressure does not exceed the yield =
strength of=20
                        the material of the finished cylinder wall. =
Calculations=20
                        must be made by the following formulas:</P><A=20
                        id=3Dr49CFR178.65-d-1></A>
                        <P id=3Dr49CFR178.65-d-1>(d)(1) Calculation of =
the stress=20
                        for cylinders must be made by the following =
formula:
                        <TABLE=20
                        style=3D"BORDER-TOP-STYLE: none; =
BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; BORDER-BOTTOM-STYLE: =
none"=20
                        xmlns:s=3D"http://states.data">
                          <TBODY>
                          <TR>
                            <TD=20
                            style=3D"BORDER-TOP-STYLE: none; =
BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; BORDER-BOTTOM-STYLE: =
none"><B></B></TD></TR>
                          <TR>
                            <TD=20
                            style=3D"BORDER-TOP-STYLE: none; =
BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; BORDER-BOTTOM-STYLE: =
none">
                              =
<P>S=3D[P(1.3D<SUP>2</SUP>+0.4d<SUP>2</SUP>)]/(D<SUP>2</SUP>=E2=80=93d<SU=
P>2</SUP>)</P></TD></TR>
                          <TR>
                            <TD=20
                            style=3D"BORDER-TOP-STYLE: none; =
BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; BORDER-BOTTOM-STYLE: =
none"><A=20
                              id=3D1></A>
                              <P>Where:</P><A id=3D2></A>
                              <P>S=3DWall stress, in psi;</P><A =
id=3D3></A>
                              <P>P=3DTest pressure in psig;</P><A =
id=3D4></A>
                              <P>D=3DOutside diameter, in inches;</P><A =
id=3D5></A>
                              <P>d=3DInside diameter, in=20
                        inches.</P></TD></TR></TBODY></TABLE></P><A=20
                        id=3Dr49CFR178.65-d-2></A>
                        <P id=3Dr49CFR178.65-d-2>(d)(2) Calculation of =
the stress=20
                        for spheres must be made by the following =
formula:
                        <TABLE=20
                        style=3D"BORDER-TOP-STYLE: none; =
BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; BORDER-BOTTOM-STYLE: =
none"=20
                        xmlns:s=3D"http://states.data">
                          <TBODY>
                          <TR>
                            <TD=20
                            style=3D"BORDER-TOP-STYLE: none; =
BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; BORDER-BOTTOM-STYLE: =
none"><B></B></TD></TR>
                          <TR>
                            <TD=20
                            style=3D"BORDER-TOP-STYLE: none; =
BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; BORDER-BOTTOM-STYLE: =
none">
                              <P>S=3DPD/4t</P></TD></TR>
                          <TR>
                            <TD=20
                            style=3D"BORDER-TOP-STYLE: none; =
BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; BORDER-BOTTOM-STYLE: =
none"><A=20
                              id=3D1></A>
                              <P>Where:</P><A id=3D2></A>
                              <P>S=3DWall stress, in psi;</P><A =
id=3D3></A>
                              <P>P=3DTest pressure in psig;</P><A =
id=3D4></A>
                              <P>D=3DOutside diameter, in inches;</P><A =
id=3D5></A>
                              <P>t=3DMinimum wall thickness, in=20
                          inches.</P></TD></TR></TBODY></TABLE></P><A=20
                        id=3Dr49CFR178.65-e></A>
                        <P id=3Dr49CFR178.65-e>(e) <B=20
                        xmlns:s=3D"http://states.data">Openings and=20
                        attachments</B>. Openings and attachments must =
conform=20
                        to the following:</P><A =
id=3Dr49CFR178.65-e-1></A>
                        <P id=3Dr49CFR178.65-e-1>(e)(1) Openings and =
attachments=20
                        are permitted on heads only.</P><A=20
                        id=3Dr49CFR178.65-e-2></A>
                        <P id=3Dr49CFR178.65-e-2>(e)(2) All openings and =
their=20
                        reinforcements must be within an imaginary =
circle,=20
                        concentric to the axis of the cylinder. The =
diameter of=20
                        the circle may not exceed 80 percent of the =
outside=20
                        diameter of the cylinder. The plane of the =
circle must=20
                        be parallel to the plane of a circumferential =
weld and=20
                        normal to the long axis of the cylinder.</P><A=20
                        id=3Dr49CFR178.65-e-3></A>
                        <P id=3Dr49CFR178.65-e-3>(e)(3) Unless a head =
has adequate=20
                        thickness, each opening must be reinforced by a =
securely=20
                        attached fitting, boss, pad, collar, or other =
suitable=20
                        means.</P><A id=3Dr49CFR178.65-e-4></A>
                        <P id=3Dr49CFR178.65-e-4>(e)(4) Material used =
for welded=20
                        openings and attachments must be of weldable =
quality and=20
                        compatible with the material of the =
cylinder.</P><A=20
                        id=3Dr49CFR178.65-f></A>
                        <P id=3Dr49CFR178.65-f>(f) <B=20
                        xmlns:s=3D"http://states.data">Pressure =
tests</B>. <A=20
                        id=3Dr49CFR178.65-f-1></A>(1) Each cylinder must =
be tested=20
                        at an internal pressure of at least the test =
pressure=20
                        and must be held at that pressure for at least =
30=20
                        seconds.</P><A id=3Dr49CFR178.65-f-1-i></A>
                        <P id=3Dr49CFR178.65-f-1-i>(f)(1)(i) The leakage =
test must=20
                        be conducted by submersion under water or by =
some other=20
                        method that will be equally sensitive.</P><A=20
                        id=3Dr49CFR178.65-f-1-ii></A>
                        <P id=3Dr49CFR178.65-f-1-ii>(f)(1)(ii) If the =
cylinder=20
                        leaks, evidences visible distortion, or any =
other=20
                        defect, while under test, it must be rejected =
(see=20
                        paragraph <A=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?chunkKey=3D090163348004773e&amp;keyword=3DDOT39 =
CYLINDER#r49CFR178.65-h">(h)</A>=20
                        of this section).</P><A =
id=3Dr49CFR178.65-f-2></A>
                        <P id=3Dr49CFR178.65-f-2>(f)(2) One cylinder =
taken from=20
                        the beginning of each lot, and one from each =
1,000 or=20
                        less successively produced within the lot =
thereafter,=20
                        must be hydrostatically tested to destruction. =
The=20
                        entire lot must be rejected (see paragraph <A=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?chunkKey=3D090163348004773e&amp;keyword=3DDOT39 =
CYLINDER#r49CFR178.65-h">(h)</A>=20
                        of this section) if:</P><A =
id=3Dr49CFR178.65-f-2-i></A>
                        <P id=3Dr49CFR178.65-f-2-i>(f)(2)(i) A failure =
occurs at a=20
                        gage pressure less than 2.0 times the test=20
                        pressure;</P><A id=3Dr49CFR178.65-f-2-ii></A>
                        <P id=3Dr49CFR178.65-f-2-ii>(f)(2)(ii) A failure =
initiates=20
                        in a braze or a weld or the heat affected zone=20
                        thereof;</P><A id=3Dr49CFR178.65-f-2-iii></A>
                        <P id=3Dr49CFR178.65-f-2-iii>(f)(2)(iii) A =
failure is=20
                        other than in the sidewall of a cylinder =
longitudinal=20
                        with its long axis; or</P><A =
id=3Dr49CFR178.65-f-2-iv></A>
                        <P id=3Dr49CFR178.65-f-2-iv>(f)(2)(iv) In a =
sphere, a=20
                        failure occurs in any opening, reinforcement, or =
at a=20
                        point of attachment.</P><A =
id=3Dr49CFR178.65-f-3></A>
                        <P id=3Dr49CFR178.65-f-3>(f)(3) A =
=E2=80=98=E2=80=98lot=E2=80=99=E2=80=99 is defined as=20
                        the quantity of cylinders successively produced =
per=20
                        production shift (not exceeding 10 hours) having =

                        identical size, design, construction, material, =
heat=20
                        treatment, finish, and quality.</P><A=20
                        id=3Dr49CFR178.65-g></A>
                        <P id=3Dr49CFR178.65-g>(g) <B=20
                        xmlns:s=3D"http://states.data">Flattening =
test</B>. One=20
                        cylinder must be taken from the beginning of =
production=20
                        of each lot (as defined in paragraph (f)(3) of =
this=20
                        section) and subjected to a flattening test as=20
                        follows:</P><A id=3Dr49CFR178.65-g-1></A>
                        <P id=3Dr49CFR178.65-g-1>(g)(1) The flattening =
test must=20
                        be made on a cylinder that has been tested at =
test=20
                        pressure.</P><A id=3Dr49CFR178.65-g-2></A>
                        <P id=3Dr49CFR178.65-g-2>(g)(2) A ring taken =
from a=20
                        cylinder may be flattened as an alternative to a =
test on=20
                        a complete cylinder. The test ring may not =
include the=20
                        heat affected zone or any weld. However, for a =
sphere,=20
                        the test ring may include the circumferential =
weld if it=20
                        is located at a 45 degree angle to the ring, =
+/=E2=88=925=20
                        degrees.</P><A id=3Dr49CFR178.65-g-3></A>
                        <P id=3Dr49CFR178.65-g-3>(g)(3) The flattening =
must be=20
                        between 60 degrees included-angle, wedge shaped =
knife=20
                        edges, rounded to a 0.5 inch radius.</P><A=20
                        id=3Dr49CFR178.65-g-4></A>
                        <P id=3Dr49CFR178.65-g-4>(g)(4) Cylinders and =
test rings=20
                        may not crack when flattened so that their outer =

                        surfaces are not more than six times wall =
thickness=20
                        apart when made of steel or not more than ten =
times wall=20
                        thickness apart when made of aluminum.</P><A=20
                        id=3Dr49CFR178.65-g-5></A>
                        <P id=3Dr49CFR178.65-g-5>(g)(5) If any cylinder =
or ring=20
                        cracks when subjected to the specified =
flattening test,=20
                        the lot of cylinders represented by the test =
must be=20
                        rejected (see paragraph <A=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?chunkKey=3D090163348004773e&amp;keyword=3DDOT39 =
CYLINDER#r49CFR178.65-h">(h)</A>=20
                        of this section).</P><A id=3Dr49CFR178.65-h></A>
                        <P id=3Dr49CFR178.65-h>(h) <B=20
                        xmlns:s=3D"http://states.data">Rejected =
cylinders</B>.=20
                        Rejected cylinders must conform to the following =

                        requirements:</P><A id=3Dr49CFR178.65-h-1></A>
                        <P id=3Dr49CFR178.65-h-1>(h)(1) If the cause for =
rejection=20
                        of a lot is determinable, and if by test or =
inspection=20
                        defective cylinders are eliminated from the lot, =
the=20
                        remaining cylinders must be qualified as a new =
lot under=20
                        paragraphs <A=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?chunkKey=3D090163348004773e&amp;keyword=3DDOT39 =
CYLINDER#r49CFR178.65-f">(f)</A>=20
                        and <A=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?chunkKey=3D090163348004773e&amp;keyword=3DDOT39 =
CYLINDER#r49CFR178.65-g">(g)</A>=20
                        of this section.</P><A =
id=3Dr49CFR178.65-h-2></A>
                        <P id=3Dr49CFR178.65-h-2>(h)(2) Repairs to welds =
are=20
                        permitted. Following repair, a cylinder must =
pass the=20
                        pressure test specified in paragraph <A=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?chunkKey=3D090163348004773e&amp;keyword=3DDOT39 =
CYLINDER#r49CFR178.65-f">(f)</A>=20
                        of this section.</P><A =
id=3Dr49CFR178.65-h-3></A>
                        <P id=3Dr49CFR178.65-h-3>(h)(3) If a cylinder =
made from=20
                        seamless steel tubing fails the flattening test=20
                        described in paragraph <A=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?chunkKey=3D090163348004773e&amp;keyword=3DDOT39 =
CYLINDER#r49CFR178.65-g">(g)</A>=20
                        of this section, suitable uniform heat treatment =
must be=20
                        used on each cylinder in the lot. All prescribed =
tests=20
                        must be performed subsequent to this heat=20
                        treatment.</P><A id=3Dr49CFR178.65-i></A>
                        <P id=3Dr49CFR178.65-i>(i) <B=20
                        xmlns:s=3D"http://states.data">Markings</B>. <A=20
                        id=3Dr49CFR178.65-i-1></A>(1) The markings =
required by=20
                        this section must be durable and waterproof. The =

                        requirements of <A=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?reg=3Dr49CFR178.35-h#r49CFR178.35-h">=C2=A7178.35(h)</A=
>=20
                        do not apply to this section.</P><A=20
                        id=3Dr49CFR178.65-i-2></A>
                        <P id=3Dr49CFR178.65-i-2>(i)(2) Required =
markings are as=20
                        follows:</P><A id=3Dr49CFR178.65-i-2-i></A>
                        <P id=3Dr49CFR178.65-i-2-i>(i)(2)(i) =
DOT-39.</P><A=20
                        id=3Dr49CFR178.65-i-2-ii></A>
                        <P id=3Dr49CFR178.65-i-2-ii>(i)(2)(ii) =
NRC.</P><A=20
                        id=3Dr49CFR178.65-i-2-iii></A>
                        <P id=3Dr49CFR178.65-i-2-iii>(i)(2)(iii) The =
service=20
                        pressure.</P><A id=3Dr49CFR178.65-i-2-iv></A>
                        <P id=3Dr49CFR178.65-i-2-iv>(i)(2)(iv) The test=20
                        pressure.</P><A id=3Dr49CFR178.65-i-2-v></A>
                        <P id=3Dr49CFR178.65-i-2-v>(i)(2)(v) The =
registration=20
                        number (M****) of the manufacturer.</P><A=20
                        id=3Dr49CFR178.65-i-2-vi></A>
                        <P id=3Dr49CFR178.65-i-2-vi>(i)(2)(vi) The lot=20
                        number.</P><A id=3Dr49CFR178.65-i-2-vii></A>
                        <P id=3Dr49CFR178.65-i-2-vii>(i)(2)(vii) The =
date of=20
                        manufacture if the lot number does not establish =
the=20
                        date of manufacture.</P><A =
id=3Dr49CFR178.65-i-2-viii></A>
                        <P id=3Dr49CFR178.65-i-2-viii>(i)(2)(viii) With =
one of the=20
                        following statements:</P><A=20
                        id=3Dr49CFR178.65-i-2-viii-A></A>
                        <P id=3Dr49CFR178.65-i-2-viii-A>(i)(2)(viii)(A) =
For=20
                        cylinders manufactured prior to October 1, 1996: =

                        =E2=80=9CFederal law forbids transportation if =
refilled-penalty=20
                        up to $25,000 fine and 5 years imprisonment (49 =
U.S.C.=20
                        1809)=E2=80=9D or =E2=80=9CFederal law forbids =
transportation if=20
                        refilled-penalty up to $500,000 fine and 5 years =

                        imprisonment (49 U.S.C. 5124).=E2=80=9D</P><A=20
                        id=3Dr49CFR178.65-i-2-viii-B></A>
                        <P id=3Dr49CFR178.65-i-2-viii-B>(i)(2)(viii)(B) =
For=20
                        cylinders manufactured on or after October 1, =
1996:=20
                        =E2=80=9CFederal law forbids transportation if =
refilled-penalty=20
                        up to $500,000 fine and 5 years imprisonment (49 =
U.S.C.=20
                        5124).=E2=80=9D</P><A id=3Dr49CFR178.65-i-3></A>
                        <P id=3Dr49CFR178.65-i-3>(i)(3) The markings =
required by=20
                        paragraphs <A=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?chunkKey=3D090163348004773e&amp;keyword=3DDOT39 =
CYLINDER#r49CFR178.65-i-2-i">(i)(2)(i)</A>=20
                        through <A=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?chunkKey=3D090163348004773e&amp;keyword=3DDOT39 =
CYLINDER#r49CFR178.65-i-2-v">(i)(2)(v)</A>=20
                        of this section must be in numbers and letters =
at least=20
                        <SUP>1</SUP><SPAN style=3D"LETTER-SPACING: =
-0.05em"=20
                        =
xmlns:s=3D"http://states.data">/<SUB>8</SUB></SPAN> inch=20
                        high and displayed sequentially. For =
example:</P><A=20
                        id=3Dr49CFR178.65-i-3-u01></A>
                        <P id=3Dr49CFR178.65-i-3-u01>DOT-39 NRC 250/500=20
                        M1001.</P><A id=3Dr49CFR178.65-i-4></A>
                        <P id=3Dr49CFR178.65-i-4>(i)(4) No person may =
mark any=20
                        cylinder with the specification identification =
=E2=80=9CDOT-39=E2=80=9D=20
                        unless it was manufactured in compliance with =
the=20
                        requirements of this section and its =
manufacturer has a=20
                        registration number (M****) from the Associate=20
                        Administrator.</P>
                        <P style=3D"FONT-SIZE: 80%"=20
                        xmlns:s=3D"http://states.data">[Amdt. =
178=E2=80=93114, 61 FR=20
                        25942, May 23, 1996, as amended at 65 FR 58631, =
Sept.=20
                        29, 2000; 66 FR 45389, Aug. 28, 2001; 67 FR =
51654, Aug.=20
                        8, 2002; 68 FR 75748, 75749, Dec. 31, =
2003]</P><LINK=20
                        =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/m=
cregis4/XMLPages/Content.css"=20
                        type=3Dtext/css rel=3Dstylesheet=20
                        xmlns:s=3D"http://states.data"><SPAN=20
                        =
id=3Dctl00_ctl00_MainContentArea_page_content_place_holder_spnSpanish></S=
PAN></DIV></TD></TR></TBODY></TABLE><SPAN=20
                  =
id=3Dctl00_ctl00_MainContentArea_page_content_place_holder_spnError><BR><=
SPAN=20
                  style=3D"COLOR: red; BACKGROUND-COLOR: white">Some =
error has=20
                  occured please try again.</SPAN></SPAN> <!--/PAGEWATCH =
CODE=3D""--><BR>
                  <DIV align=3Dright><A=20
                  =
href=3D"http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/f=
mcsrruletext.aspx?chunkKey=3D090163348004773e&amp;keyword=3DDOT39 =
CYLINDER#top"><IMG=20
                  height=3D15 alt=3D"Go To Top of Page"=20
                  =
src=3D"http://www.fmcsa.dot.gov/images/new_style/topArrow.gif"=20
                  width=3D15 border=3D0></A></DIV></TD>
                <TD vAlign=3Dtop><!--	</td> --><!-- closing td tag is =
commented to fix the formatting issue--><!-- Content Ends --></TD>
                <TD height=3D"100%">&nbsp;=20
      </TD></TR></TBODY></TABLE></TD></TR></TBODY></TABLE><!-- footer-->
      <TABLE class=3Dfooter id=3DtblFooter cellSpacing=3D0 =
cellPadding=3D3 width=3D"100%"=20
      border=3D0>
        <TBODY>
        <TR>
          <TD width=3D117></TD>
          <TD class=3Dtri=20
          style=3D"BACKGROUND-IMAGE: =
url(/images/new_style/footer_center.jpg)"=20
          align=3Dmiddle><A class=3Dfooter=20
            href=3D"http://www.fmcsa.dot.gov/feedback.htm">Feedback</A> =
| <A=20
            class=3Dfooter=20
            =
href=3D"http://www.fmcsa.dot.gov/redirect.aspx?page=3Dhttp://www.dot.gov/=
privacy.html">Privacy=20
            Policy</A> | <A class=3Dfooter=20
            title=3D"USA.gov: The U.S. Government Homepage"=20
            =
href=3D"http://www.fmcsa.dot.gov/redirect.aspx?page=3Dhttp://www.USA.gov"=
>USA.gov</A>=20
            | <A class=3Dfooter=20
            href=3D"http://www.fmcsa.dot.gov/foia/index.htm">Freedom of=20
            Information Act (FOIA)</A> | <A class=3Dfooter=20
            =
href=3D"http://www.fmcsa.dot.gov/508disclaimer.htm">Accessibility</A>=20
            <BR><A class=3Dfooter=20
            title=3D"Office of Inspector General (OIG) Hotline Complaint =
Center"=20
            =
href=3D"http://www.fmcsa.dot.gov/redirect.aspx?page=3Dhttps://www.oig.dot=
.gov/Hotline">OIG=20
            Hotline</A> | <A class=3Dfooter=20
            =
href=3D"http://www.fmcsa.dot.gov/about/WebPoliciesAndImportantLinks.htm">=
Web=20
            Policies and Important Links</A> | <A class=3Dfooter=20
            href=3D"http://www.fmcsa.dot.gov/sitemap.asp">Site Map</A> | =
<A=20
            class=3Dfooter title=3D"Get plug-ins for popular =
applications"=20
            href=3D"http://www.fmcsa.dot.gov/plugins.htm">Plug-ins</A> =
<BR><BR><A=20
            class=3Dfooter href=3D"http://www.fmcsa.dot.gov/">Federal =
Motor Carrier=20
            Safety Administration</A><BR>1200 New Jersey Avenue SE, =
Washington,=20
            DC 20590 =E2=80=A2 ************** =E2=80=A2 TTY: =
**************<BR><A class=3Dfooter=20
            =
href=3D"http://www.fmcsa.dot.gov/about/contact/offices/displayfieldroster=
.asp">Field=20
            Office Phone List</A>&nbsp;</TD>
          <TD =
width=3D117></TD></TR></TBODY></TABLE></TD></TR></TBODY></TABLE></DIV>
<DIV><INPUT id=3D__EVENTTARGET type=3Dhidden name=3D__EVENTTARGET> =
<INPUT=20
id=3D__EVENTARGUMENT type=3Dhidden name=3D__EVENTARGUMENT> <INPUT =
id=3D__EVENTVALIDATION=20
type=3Dhidden=20
value=3D/wEWFgKEgtX3CAL29NzDDALmzYTdAgK/yOCSDgLTo5msDgLDzuHNCQK15cGuCgKYi=
rz9DgL5i6S3CwLimMRlAo7KqvwLAtGlgJIHAtKlgJIHAuSlgJIHAtOlgJIHAualgJIHAtelgJ=
IHAvmLpLcLAuKYxGUCspew8wwCoOu49AwCncT8tgg9li1H64qVjMA46f9e3y5dE4eAew=3D=3D=
=20
name=3D__EVENTVALIDATION> </DIV>
<SCRIPT type=3Dtext/javascript>
//<![CDATA[
var theForm =3D document.forms['aspnetForm'];
if (!theForm) {
    theForm =3D document.aspnetForm;
}
function __doPostBack(eventTarget, eventArgument) {
    if (!theForm.onsubmit || (theForm.onsubmit() !=3D false)) {
        theForm.__EVENTTARGET.value =3D eventTarget;
        theForm.__EVENTARGUMENT.value =3D eventArgument;
        theForm.submit();
    }
}
//]]>
</SCRIPT>

<SCRIPT=20
src=3D"http://www.fmcsa.dot.gov/WebResource.axd?d=3DUPcOHGV5X_2qK7_t7-Ee3=
Q2&amp;t=3D633338582618053248"=20
type=3Dtext/javascript></SCRIPT>
</FORM></BODY></HTML>

------=_NextPart_000_0000_01CA4DB9.92AFE550
Content-Type: image/gif
Content-Transfer-Encoding: base64
Content-Location: http://www.fmcsa.dot.gov/images/style/1-pixel.gif

R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==

------=_NextPart_000_0000_01CA4DB9.92AFE550
Content-Type: image/gif
Content-Transfer-Encoding: base64
Content-Location: http://www.fmcsa.dot.gov/images/dot.gif

R0lGODlhDwAPAPcAAAAAAP///9XO1NLR1IyLmF9fbnh4hExQaV1ifJGVqk1YiFZnnDtev0VmwC08
ZrG3yRs7jRUrZCVInCZEkyA0Zz1ivD9dpThPiXKHunSCpBIyeSBEkyVLnSNEkBszZjFbty5TpyVD
hTRbr0FnwUBmvERrwkVsv0xzxjZMe0BUgk9llWZ7q4OUu3uLrn6LpxY7hxo/ih9GlyZTqiRMnyVJ
kCpOmC5VpTdgtzhhs0BtyTpjtUJvyVSH709+21GB3VSD3klwwlB70Ux2yFmI5mKR9FV/1GCQ7ENi
oWWR5nCc8UFZi2OJ01l0rXGT1nOT0XCBoo6gxpqmvZ6iqnFzdx9LnihasShRni9ftEBvxD5pukh3
zU+C4Ep5y16P62+h+k5wsEZhkGeEuWd/q42l0oOYvXiGoHR+kaizx624yyVVp3aq/3Ok8k1rnGeM
ynyo7X2i31lwl2iDr1JmiKCz07C902WX44Gz/Xyo7E1tmIa6/22GqHqv8oa7/ou++nmj2YCbu5ez
1GZ3i7nG1oW+/Etqjo7E/qi1xI7C+J3N/DxNX7nF0Z+ho4nD+o7D85DC75ayzaG705rP+57S/Z66
0qa8z6S0waTF3rTI153T+MXM0Zi806Tc+6jd+7S+xLHj/LPo/snO0Kjl+7Pr+qvY5bPV33uChL/0
/7f1/tjd3LS3sMjKxdHRy9PSwsrIutHNwMTBvcnCu9fOycjBva+qp9jRzv///wAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAEAALUALAAAAAAPAA8A
QAjhAGsJFKiIEqQ5dAYKZKMmCQtasV6pkkXAAoMqHQ7U0vTJE6dIfQC5atKlCJYJA+UQ+qADhwgb
VqjAiKAwE6lToSx1euOFyAg9Av8MYnSIzx0oAlq12SKkQRowtUhgkMLFR48dRwKVmlLAQxmFtZ6o
uKAETgKwAlFVmvTIz5o6X6IoBDXKlKhNjS4ZQmLkx5UWtQQ5wiQJUaE9aFg5GRKkxIwMePLYcTPm
waoBsOJoAVJBBgoyPJakOhOGiYtFswwoSJEIQa0VC8TkOGEiyw0QNWhAoKDQTIgYHCRseKHBwcCA
ADs=

------=_NextPart_000_0000_01CA4DB9.92AFE550
Content-Type: image/gif
Content-Transfer-Encoding: base64
Content-Location: http://www.fmcsa.dot.gov/images/printerimage.gif

R0lGODlhEAAOAKIEAFtoVuTm49PX0qato////wAAAAAAAAAAACH5BAEAAAQALAAAAAAQAA4AAAM0
SKrQ7muBQCsFkYU3cZ5W5UXds0FaeG0bwXGso1rd+b4XaAn8iumVgTD3u8E8wFCHZGwsEgA7

------=_NextPart_000_0000_01CA4DB9.92AFE550
Content-Type: image/jpeg
Content-Transfer-Encoding: base64
Content-Location: http://www.fmcsa.dot.gov/images/style/arrow_left_up.jpg

/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0a
HBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIy
MjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCAAJAAgDASIA
AhEBAxEB/8QAFgABAQEAAAAAAAAAAAAAAAAAAAYH/8QAIBAAAQMEAgMAAAAAAAAAAAAAAQAFEgID
BBEGEwcUIf/EABQBAQAAAAAAAAAAAAAAAAAAAAL/xAAXEQEAAwAAAAAAAAAAAAAAAAABABIx/9oA
DAMBAAIRAxEAPwCxfc7lbF5KozZu7gz14wPrN7ZVdtAGsjrImBPQl2b3sgRj8RaciFUxjsOk/9k=

------=_NextPart_000_0000_01CA4DB9.92AFE550
Content-Type: image/gif
Content-Transfer-Encoding: base64
Content-Location: http://www.fmcsa.dot.gov/images/new_style/topArrow.gif

R0lGODlhDwAPAKIHAKAWHYw7Oo4vL596daaKhJsfJIw6OQAAACH5BAEAAAcALAAAAAAPAA8AAAM7
eLrcPAIYIIY7AWisQ8ubAnrKoAGfZh0R6mSCUrhXpNDXQdxnc7ot3OEUO5hCi9OKI+ykfk4HhLjM
5RIAOw==

------=_NextPart_000_0000_01CA4DB9.92AFE550
Content-Type: text/css;
	charset="iso-8859-1"
Content-Transfer-Encoding: quoted-printable
Content-Location: http://www.fmcsa.dot.gov/Style/style.css

BODY {
	MARGIN-TOP: 0px; FONT-SIZE: small; BACKGROUND-REPEAT: repeat; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; BACKGROUND-COLOR: =
#e8e8e8
}
BODY.print {
	MARGIN-TOP: 0px; FONT-SIZE: small; BACKGROUND-REPEAT: repeat; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; BACKGROUND-COLOR: =
#ffffff
}
TR.utl {
	BACKGROUND-POSITION: left bottom; PADDING-LEFT: 2px; FONT-SIZE: 10px; =
VERTICAL-ALIGN: middle; PADDING-TOP: 2px; BACKGROUND-REPEAT: repeat-x; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif
}
A.utl:link {
	FONT-SIZE: 10px; COLOR: #a0161d; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: none
}
A.utl:visited {
	FONT-SIZE: 10px; COLOR: #a0161d; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: none
}
A.utl:active {
	FONT-SIZE: 10px; COLOR: #a0161d; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: none
}
A.utl:hover {
	FONT-SIZE: 10px; COLOR: #a0161d; TEXT-DECORATION: underline
}
.utl {
	FONT-SIZE: 10px; VERTICAL-ALIGN: middle; COLOR: #a0161d; FONT-FAMILY: =
arial, tahoma, verdana, sans-serif; TEXT-DECORATION: none
}
.utl_select {
	FONT-SIZE: 10px; VERTICAL-ALIGN: middle; FONT-FAMILY: arial, tahoma, =
verdana, sans-serif; HEIGHT: 20px; TEXT-DECORATION: none
}
TR.banneroverrun {
	BACKGROUND-COLOR: #08142e
}
TD.bannerimage {
	WIDTH: 755px; BACKGROUND-REPEAT: no-repeat; HEIGHT: 90px
}
TD.bannerimageThin {
	WIDTH: 755px; BACKGROUND-REPEAT: no-repeat; HEIGHT: 54px
}
P.hdr {
	FONT-SIZE: 24px; MARGIN-BOTTOM: 2px; MARGIN-LEFT: 10px; COLOR: #fff; =
FONT-STYLE: normal; FONT-FAMILY: Georgia, times roman, serif
}
P.hdrThin {
	MARGIN-TOP: 2px; FONT-SIZE: 20px; MARGIN-BOTTOM: 0px; MARGIN-LEFT: =
10px; COLOR: #fff; FONT-STYLE: normal; FONT-FAMILY: Georgia, times =
roman, serif
}
P.hdrThinsub {
	MARGIN-TOP: 0px; FONT-SIZE: 14px; MARGIN-LEFT: 10px; COLOR: #fff; =
FONT-STYLE: normal; FONT-FAMILY: georgia, times roman, serif
}
TABLE.mission {
	BORDER-RIGHT: #555 1px solid; PADDING-RIGHT: 0px; BORDER-TOP: #555 1px =
solid; PADDING-LEFT: 0px; FONT-WEIGHT: bold; FONT-SIZE: 12px; =
PADDING-BOTTOM: 2px; BORDER-LEFT: #555 1px solid; COLOR: #fff; =
PADDING-TOP: 2px; BORDER-BOTTOM: #555 1px solid; FONT-STYLE: italic; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; BACKGROUND-COLOR: =
#172b65
}
TD.cell {
	BORDER-LEFT: #fff 1px solid; BORDER-BOTTOM: #fff 1px solid
}
TD.HomeCell {
	BORDER-BOTTOM: #fff 1px solid
}
TD.cellMultiLevel {
	BORDER-LEFT: #fff 1px solid; BORDER-BOTTOM: #cccccc 1px solid
}
TD.HomeCellMultiLevel {
	BORDER-BOTTOM: #cccccc 1px solid
}
TD.LevelTwoNav {
	BACKGROUND-COLOR: #cccccc
}
TD.topNavTD {
	BACKGROUND-COLOR: #a0161d
}
A.navi:link {
	DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; PADDING-BOTTOM: =
3px; COLOR: #fff; PADDING-TOP: 3px; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; BACKGROUND-COLOR: #a0161d; TEXT-DECORATION: none
}
A.navi:visited {
	DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; PADDING-BOTTOM: =
3px; COLOR: #fff; PADDING-TOP: 3px; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; BACKGROUND-COLOR: #a0161d; TEXT-DECORATION: none
}
A.navi:active {
	DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; PADDING-BOTTOM: =
3px; COLOR: #fff; PADDING-TOP: 3px; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; BACKGROUND-COLOR: #a0161d; TEXT-DECORATION: none
}
A.navi:hover {
	DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; PADDING-BOTTOM: =
3px; COLOR: #333; PADDING-TOP: 3px; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; BACKGROUND-COLOR: #c7d3e0; TEXT-DECORATION: none
}
A.naviFirstHighlight:active {
	DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; PADDING-BOTTOM: =
3px; PADDING-TOP: 3px; FONT-FAMILY: arial, tahoma, verdana, sans-serif; =
BACKGROUND-COLOR: #ffffff; TEXT-DECORATION: none; BORDER-BOTTOM-STYLE: =
none
}
A.naviFirstHighlight:link {
	DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; PADDING-BOTTOM: =
3px; PADDING-TOP: 3px; FONT-FAMILY: arial, tahoma, verdana, sans-serif; =
BACKGROUND-COLOR: #ffffff; TEXT-DECORATION: none; BORDER-BOTTOM-STYLE: =
none
}
A.naviFirstHighlight:hover {
	DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; PADDING-BOTTOM: =
3px; PADDING-TOP: 3px; FONT-FAMILY: arial, tahoma, verdana, sans-serif; =
BACKGROUND-COLOR: #ffffff; TEXT-DECORATION: none; BORDER-BOTTOM-STYLE: =
none
}
A.naviFirstHighlight:visited {
	DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; PADDING-BOTTOM: =
3px; PADDING-TOP: 3px; FONT-FAMILY: arial, tahoma, verdana, sans-serif; =
BACKGROUND-COLOR: #ffffff; TEXT-DECORATION: none; BORDER-BOTTOM-STYLE: =
none
}
A.naviFirstHighlightMultiLevel:active {
	DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; PADDING-BOTTOM: =
3px; COLOR: #333; PADDING-TOP: 3px; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; BACKGROUND-COLOR: #cccccc; TEXT-DECORATION: none; =
BORDER-BOTTOM-STYLE: none
}
A.naviFirstHighlightMultiLevel:link {
	DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; PADDING-BOTTOM: =
3px; COLOR: #333; PADDING-TOP: 3px; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; BACKGROUND-COLOR: #cccccc; TEXT-DECORATION: none; =
BORDER-BOTTOM-STYLE: none
}
A.naviFirstHighlightMultiLevel:hover {
	DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; PADDING-BOTTOM: =
3px; COLOR: #333; PADDING-TOP: 3px; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; BACKGROUND-COLOR: #cccccc; TEXT-DECORATION: none; =
BORDER-BOTTOM-STYLE: none
}
A.naviFirstHighlightMultiLevel:visited {
	DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; PADDING-BOTTOM: =
3px; COLOR: #333; PADDING-TOP: 3px; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; BACKGROUND-COLOR: #cccccc; TEXT-DECORATION: none; =
BORDER-BOTTOM-STYLE: none
}
.contentContainerbread {
	BORDER-RIGHT: #555 1px solid; BORDER-LEFT: #555 1px solid; =
BACKGROUND-COLOR: #fff
}
.breadcrumbs {
	FONT-SIZE: x-small; MARGIN-BOTTOM: 1px; FONT-FAMILY: arial, tahoma, =
verdana, sans-serif
}
A.breadcrumbs:link {
	FONT-SIZE: x-small; COLOR: #033591; TEXT-DECORATION: none
}
A.breadcrumbs:visited {
	FONT-SIZE: x-small; COLOR: #033591; TEXT-DECORATION: none
}
A.breadcrumbs:active {
	FONT-SIZE: x-small; COLOR: #033591; TEXT-DECORATION: none
}
A.breadcrumbs:hover {
	FONT-SIZE: x-small; COLOR: #033591; TEXT-DECORATION: underline
}
.breadcrumbswhite {
	FONT-SIZE: x-small; MARGIN-BOTTOM: 1px; FONT-FAMILY: arial, tahoma, =
verdana, sans-serif
}
A.breadcrumbswhite:link {
	FONT-SIZE: x-small; COLOR: #fff; TEXT-DECORATION: none
}
A.breadcrumbswhite:visited {
	FONT-SIZE: x-small; COLOR: #fff; TEXT-DECORATION: none
}
A.breadcrumbswhite:active {
	FONT-SIZE: x-small; COLOR: #fff; TEXT-DECORATION: none
}
A.breadcrumbswhite:hover {
	FONT-SIZE: x-small; COLOR: #fff; TEXT-DECORATION: underline
}
TD.leftNavMain2 {
	BORDER-TOP: #798ebd 1px solid; BORDER-LEFT: #555 0px solid; =
BACKGROUND-REPEAT: no-repeat
}
.TOCHeadSelectedTD {
	BORDER-RIGHT: #798ebd 0px solid; BACKGROUND-POSITION: left 50%; =
BORDER-TOP: #798ebd 1px solid; PADDING-LEFT: 5px; FONT-WEIGHT: bold; =
FONT-SIZE: small; PADDING-BOTTOM: 4px; BORDER-LEFT: #798ebd 1px solid; =
COLOR: #000000; PADDING-TOP: 16px; BORDER-BOTTOM: #798ebd 1px solid; =
FONT-FAMILY: Arial, Verdana, sans-serif; BACKGROUND-COLOR: #ffffff; =
TEXT-ALIGN: left
}
.TOCSelectedTD {
	BORDER-RIGHT: #798ebd 0px solid; BACKGROUND-POSITION: left 50%; =
BORDER-TOP: #798ebd 1px solid; PADDING-LEFT: 15px; FONT-SIZE: small; =
PADDING-BOTTOM: 2px; BORDER-LEFT: #798ebd 1px solid; COLOR: #000000; =
PADDING-TOP: 2px; BORDER-BOTTOM: #798ebd 1px solid; FONT-FAMILY: Arial, =
Verdana, sans-serif; BACKGROUND-COLOR: #ffffff; TEXT-ALIGN: left
}
.TOCHeadTD {
	BORDER-RIGHT: #798ebd 1px solid; BORDER-TOP: #798ebd 0px solid; =
PADDING-LEFT: 8px; FONT-WEIGHT: bold; FONT-SIZE: small; PADDING-BOTTOM: =
4px; BORDER-LEFT: #798ebd 0px solid; COLOR: #000000; PADDING-TOP: 16px; =
BORDER-BOTTOM: #798ebd 0px solid; FONT-FAMILY: Arial, Verdana, =
sans-serif; BACKGROUND-COLOR: #c7d3e0; TEXT-ALIGN: left
}
.TOCUnselectedTD {
	BORDER-RIGHT: #798ebd 1px solid; BORDER-TOP: #798ebd 0px solid; =
PADDING-LEFT: 15px; PADDING-BOTTOM: 3px; BORDER-LEFT: #798ebd 0px solid; =
PADDING-TOP: 3px; BORDER-BOTTOM: #798ebd 0px solid; TEXT-ALIGN: left
}
TD.TOCSelectedTD A:active {
	DISPLAY: block; FONT-SIZE: small; COLOR: #033591; FONT-FAMILY: arial, =
verdana, sans-serif; TEXT-DECORATION: none
}
TD.TOCSelectedTD A:visited {
	DISPLAY: block; FONT-SIZE: small; COLOR: #033591; FONT-FAMILY: arial, =
verdana, sans-serif; TEXT-DECORATION: none
}
TD.TOCSelectedTD A:link {
	DISPLAY: block; FONT-SIZE: small; COLOR: #033591; FONT-FAMILY: arial, =
verdana, sans-serif; TEXT-DECORATION: none
}
TD.TOCSelectedTD A:hover {
	DISPLAY: block; FONT-SIZE: small; COLOR: #033591; FONT-FAMILY: arial, =
verdana, sans-serif; TEXT-DECORATION: underline
}
TD.TOCHeadSelectedTD A:active {
	DISPLAY: block; TEXT-DECORATION: none
}
TD.TOCHeadSelectedTD A:visited {
	DISPLAY: block; TEXT-DECORATION: none
}
TD.TOCHeadSelectedTD A:link {
	DISPLAY: block; TEXT-DECORATION: none
}
TD.TOCHeadSelectedTD A:hover {
	DISPLAY: block; TEXT-DECORATION: underline
}
TD.TOCUnselectedTD A:active {
	DISPLAY: block; FONT-SIZE: small; COLOR: #033591; FONT-FAMILY: arial, =
verdana, sans-serif; TEXT-DECORATION: none
}
TD.TOCUnselectedTD A:visited {
	DISPLAY: block; FONT-SIZE: small; COLOR: #033591; FONT-FAMILY: arial, =
verdana, sans-serif; TEXT-DECORATION: none
}
TD.TOCUnselectedTD A:link {
	DISPLAY: block; FONT-SIZE: small; COLOR: #033591; FONT-FAMILY: arial, =
verdana, sans-serif; TEXT-DECORATION: none
}
TD.TOCUnselectedTD A:hover {
	FONT-SIZE: small; COLOR: #033591; FONT-FAMILY: arial, verdana, =
sans-serif; TEXT-DECORATION: underline
}
TD.TOCHeadTD A:link {
	COLOR: #000000; TEXT-DECORATION: none
}
TD.TOCHeadTD A:active {
	COLOR: #000000; TEXT-DECORATION: none
}
TD.TOCHeadTD A:visited {
	COLOR: #000000; TEXT-DECORATION: none
}
TD.TOCHeadTD A:hover {
	DISPLAY: block; COLOR: #000000; TEXT-DECORATION: underline
}
TABLE.leftNavAndContenttable {
	BORDER-LEFT: #555 1px solid; BACKGROUND-COLOR: #c7d3e0
}
TABLE.footer {
	BORDER-RIGHT: #555 1px solid; BORDER-TOP: #555 1px solid; FONT-SIZE: =
11px; BORDER-LEFT: #555 1px solid; BORDER-BOTTOM: #555 1px solid; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; BACKGROUND-COLOR: =
#ffffff
}
TABLE.footernoborder {
	BORDER-TOP: #555 1px solid; FONT-SIZE: 11px; FONT-FAMILY: arial, =
tahoma, verdana, sans-serif; BACKGROUND-COLOR: #ffffff
}
TD.tri {
	BACKGROUND-POSITION: center top; BACKGROUND-REPEAT: no-repeat
}
A.footer:link {
	FONT-SIZE: 11px; COLOR: #033591; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: none
}
A.footer:visited {
	FONT-SIZE: 11px; COLOR: #033591; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: none
}
A.footer:active {
	FONT-SIZE: 11px; COLOR: #033591; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: none
}
A.footer:hover {
	FONT-SIZE: 11px; COLOR: #033591; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: underline
}
TD.contentareanobullets {
	BORDER-RIGHT: #555 1px solid; BACKGROUND-COLOR: #fff
}
TD.contentareanobullets UL {
	MARGIN: 0px; LIST-STYLE-TYPE: none
}
TD.contentareanobullets OL {
	MARGIN: 0px; LIST-STYLE-TYPE: none
}
TD.contentareanobullets LI {
	FONT-SIZE: small; PADDING-BOTTOM: 2px; MARGIN-LEFT: 0px; COLOR: #333; =
PADDING-TOP: 2px; FONT-FAMILY: arial, tahoma, verdana, sans-serif; =
LIST-STYLE-TYPE: none
}
TD.contentareanobullets LI LI {
	FONT-SIZE: small; PADDING-BOTTOM: 2px; MARGIN-LEFT: 15px; COLOR: #333; =
PADDING-TOP: 2px; FONT-FAMILY: arial, tahoma, verdana, sans-serif; =
LIST-STYLE-TYPE: none
}
TD.contentareanobullets LI LI LI {
	FONT-SIZE: small; PADDING-BOTTOM: 2px; MARGIN-LEFT: 15px; COLOR: #333; =
PADDING-TOP: 2px; FONT-FAMILY: arial, tahoma, verdana, sans-serif; =
LIST-STYLE-TYPE: none
}
TD.contentarea {
	BORDER-RIGHT: #555 1px solid; BACKGROUND-COLOR: #fff
}
TD.contentarea UL {
	PADDING-RIGHT: 0px; MARGIN-TOP: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: =
0px; MARGIN-LEFT: 20px; PADDING-TOP: 0px
}
TD.contentarea OL {
	PADDING-RIGHT: 0px; MARGIN-TOP: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: =
0px; MARGIN-LEFT: 20px; PADDING-TOP: 0px
}
TD.contentarea LI {
	FONT-SIZE: small; LIST-STYLE-IMAGE: url(/images/new_style/li_1.gif); =
FONT-FAMILY: arial, tahoma, verdana, sans-serif
}
TD.contentarea LI LI {
	FONT-SIZE: small; LIST-STYLE-IMAGE: url(/images/new_style/li_2.gif); =
FONT-FAMILY: arial, tahoma, verdana, sans-serif
}
TD.contentarea LI LI LI {
	FONT-SIZE: small; LIST-STYLE-IMAGE: url(/images/new_style/li_3.gif); =
FONT-FAMILY: arial, tahoma, verdana, sans-serif
}
UL.nobullets {
	MARGIN-LEFT: 20px; LIST-STYLE-TYPE: none
}
UL.nobullets LI {
	FONT-SIZE: small; PADDING-BOTTOM: 1px; MARGIN-LEFT: 0px; COLOR: #333; =
PADDING-TOP: 1px; FONT-FAMILY: arial, tahoma, verdana, sans-serif; =
LIST-STYLE-TYPE: none
}
UL.nobullets LI UL LI {
	FONT-SIZE: small; PADDING-BOTTOM: 1px; MARGIN-LEFT: -30px; COLOR: #333; =
PADDING-TOP: 1px; FONT-FAMILY: arial, tahoma, verdana, sans-serif; =
LIST-STYLE-TYPE: none
}
UL.bullets {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: 0px; =
MARGIN-LEFT: 20px; PADDING-TOP: 0px
}
UL.bullets LI {
	FONT-SIZE: small; PADDING-BOTTOM: 1px; PADDING-TOP: 1px; FONT-FAMILY: =
arial, tahoma, verdana, sans-serif
}
UL.bullets LI LI {
	FONT-SIZE: small; PADDING-BOTTOM: 1px; MARGIN-LEFT: -20px; PADDING-TOP: =
1px; FONT-FAMILY: arial, tahoma, verdana, sans-serif
}
UL.bullets LI LI LI {
	FONT-SIZE: small; PADDING-BOTTOM: 1px; PADDING-TOP: 1px; FONT-FAMILY: =
arial, tahoma, verdana, sans-serif
}
TD.contentarea OL {
	PADDING-RIGHT: 0px; MARGIN-TOP: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: =
0px; MARGIN-LEFT: 20px; PADDING-TOP: 0px
}
TD.contentarea OL LI {
	FONT-SIZE: small; LIST-STYLE-IMAGE: none; MARGIN-LEFT: 15px; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif
}
TD.contentarea OL LI LI {
	FONT-SIZE: small; LIST-STYLE-IMAGE: none; FONT-FAMILY: arial, tahoma, =
verdana, sans-serif
}
TD.contentarea OL LI LI LI {
	FONT-SIZE: small; LIST-STYLE-IMAGE: none; FONT-FAMILY: arial, tahoma, =
verdana, sans-serif
}
TABLE.alert {
	BACKGROUND-COLOR: #fff
}
TABLE.news {
	BACKGROUND-COLOR: #fff
}
TABLE.notice {
	BACKGROUND-COLOR: #fff
}
TABLE.rellink {
	BACKGROUND-COLOR: #fff
}
TABLE.alert UL {
	PADDING-RIGHT: 0px; DISPLAY: block; PADDING-LEFT: 0px; =
LIST-STYLE-IMAGE: none; PADDING-BOTTOM: 0px; MARGIN: 0px; PADDING-TOP: =
0px; LIST-STYLE-TYPE: none
}
TABLE.news UL {
	PADDING-RIGHT: 0px; DISPLAY: block; PADDING-LEFT: 0px; =
LIST-STYLE-IMAGE: none; PADDING-BOTTOM: 0px; MARGIN: 0px; PADDING-TOP: =
0px; LIST-STYLE-TYPE: none
}
TABLE.notice UL {
	PADDING-RIGHT: 0px; DISPLAY: block; PADDING-LEFT: 0px; =
LIST-STYLE-IMAGE: none; PADDING-BOTTOM: 0px; MARGIN: 0px; PADDING-TOP: =
0px; LIST-STYLE-TYPE: none
}
TABLE.rellink UL {
	PADDING-RIGHT: 0px; DISPLAY: block; PADDING-LEFT: 0px; =
LIST-STYLE-IMAGE: none; PADDING-BOTTOM: 0px; MARGIN: 0px; PADDING-TOP: =
0px; LIST-STYLE-TYPE: none
}
TABLE.alert LI {
	BORDER-RIGHT: #a0161d 1px solid; FONT-SIZE: 11px; LIST-STYLE-IMAGE: =
none; MARGIN-LEFT: 0px; BORDER-LEFT: #a0161d 1px solid; COLOR: #555; =
BORDER-BOTTOM: #a0161d 1px solid; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; LIST-STYLE-TYPE: none
}
TABLE.news LI {
	BORDER-RIGHT: #555 1px solid; FONT-SIZE: 11px; LIST-STYLE-IMAGE: none; =
MARGIN-LEFT: 0px; BORDER-LEFT: #555 1px solid; COLOR: #555; =
BORDER-BOTTOM: #555 1px solid; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; LIST-STYLE-TYPE: none
}
TABLE.notice LI {
	BORDER-RIGHT: #555 1px solid; FONT-SIZE: 11px; LIST-STYLE-IMAGE: none; =
MARGIN-LEFT: 0px; BORDER-LEFT: #555 1px solid; COLOR: #555; =
BORDER-BOTTOM: #555 1px solid; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; LIST-STYLE-TYPE: none
}
TABLE.rellink LI {
	BORDER-RIGHT: #555 1px solid; FONT-SIZE: 11px; LIST-STYLE-IMAGE: none; =
MARGIN-LEFT: 0px; BORDER-LEFT: #555 1px solid; COLOR: #555; =
BORDER-BOTTOM: #555 1px solid; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; LIST-STYLE-TYPE: none
}
TD.hdr {
	BORDER-RIGHT: #555 1px solid; BACKGROUND-POSITION: left top; =
BORDER-TOP: #555 1px solid; PADDING-LEFT: 4px; FONT-WEIGHT: bold; =
FONT-SIZE: 12px; BORDER-LEFT: #555 1px solid; COLOR: #444; =
BORDER-BOTTOM: #555 1px solid; BACKGROUND-REPEAT: repeat-x; FONT-FAMILY: =
arial, tahoma, verdana, sans-serif; HEIGHT: 20px
}
TD.alerthdr {
	BORDER-RIGHT: #a0161d 1px solid; BACKGROUND-POSITION: left top; =
BORDER-TOP: #a0161d 1px solid; PADDING-LEFT: 4px; FONT-WEIGHT: bold; =
FONT-SIZE: 12px; BORDER-LEFT: #a0161d 1px solid; COLOR: #444; =
BORDER-BOTTOM: #a0161d 1px solid; BACKGROUND-REPEAT: repeat-x; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; HEIGHT: 20px
}
A.alert:link {
	PADDING-RIGHT: 3px; DISPLAY: block; PADDING-LEFT: 3px; FONT-SIZE: =
x-small; PADDING-BOTTOM: 3px; COLOR: #a0161d; PADDING-TOP: 3px; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; TEXT-DECORATION: none
}
A.alert:visited {
	PADDING-RIGHT: 3px; DISPLAY: block; PADDING-LEFT: 3px; FONT-SIZE: =
x-small; PADDING-BOTTOM: 3px; COLOR: #a0161d; PADDING-TOP: 3px; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; TEXT-DECORATION: none
}
A.alert:active {
	PADDING-RIGHT: 3px; DISPLAY: block; PADDING-LEFT: 3px; FONT-SIZE: =
x-small; PADDING-BOTTOM: 3px; COLOR: #a0161d; PADDING-TOP: 3px; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; TEXT-DECORATION: none
}
A.alert:hover {
	PADDING-RIGHT: 3px; DISPLAY: block; PADDING-LEFT: 3px; FONT-SIZE: =
x-small; PADDING-BOTTOM: 3px; COLOR: #333; PADDING-TOP: 3px; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; BACKGROUND-COLOR: =
#bfd3e5; TEXT-DECORATION: none
}
A.news:link {
	PADDING-RIGHT: 3px; DISPLAY: block; PADDING-LEFT: 3px; FONT-SIZE: =
x-small; PADDING-BOTTOM: 3px; COLOR: #033591; PADDING-TOP: 3px; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; TEXT-DECORATION: none
}
A.news:visited {
	PADDING-RIGHT: 3px; DISPLAY: block; PADDING-LEFT: 3px; FONT-SIZE: =
x-small; PADDING-BOTTOM: 3px; COLOR: #033591; PADDING-TOP: 3px; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; TEXT-DECORATION: none
}
A.news:active {
	PADDING-RIGHT: 3px; DISPLAY: block; PADDING-LEFT: 3px; FONT-SIZE: =
x-small; PADDING-BOTTOM: 3px; COLOR: #033591; PADDING-TOP: 3px; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; TEXT-DECORATION: none
}
A.news:hover {
	PADDING-RIGHT: 3px; DISPLAY: block; PADDING-LEFT: 3px; FONT-SIZE: =
x-small; PADDING-BOTTOM: 3px; COLOR: #333; PADDING-TOP: 3px; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; BACKGROUND-COLOR: =
#bfd3e5; TEXT-DECORATION: none
}
A.newsheading:link {
	FONT-SIZE: 12px; COLOR: #555; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif
}
A.newsheading:visited {
	FONT-SIZE: 12px; COLOR: #555; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif
}
A.newsheading:active {
	FONT-SIZE: 12px; COLOR: #555; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif
}
A.newsheading:hover {
	FONT-SIZE: 12px; COLOR: #555; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: underline
}
TD.rlhdr {
	BORDER-RIGHT: #555 1px solid; BACKGROUND-POSITION: left top; =
BORDER-TOP: #555 1px solid; PADDING-LEFT: 4px; FONT-WEIGHT: bold; =
FONT-SIZE: 12px; BORDER-LEFT: #555 1px solid; COLOR: #444; =
BORDER-BOTTOM: #555 1px solid; BACKGROUND-REPEAT: repeat-x; FONT-FAMILY: =
arial, tahoma, verdana, sans-serif; HEIGHT: 20px; BACKGROUND-COLOR: =
#c0c0c0
}
UL.rellinks {
	PADDING-RIGHT: 0px; DISPLAY: block; PADDING-LEFT: 0px; =
LIST-STYLE-IMAGE: none; PADDING-BOTTOM: 0px; MARGIN: 0px; PADDING-TOP: =
0px; LIST-STYLE-TYPE: none
}
LI.rellinks {
	PADDING-RIGHT: 0px; DISPLAY: block; PADDING-LEFT: 0px; =
LIST-STYLE-IMAGE: none; PADDING-BOTTOM: 0px; MARGIN: 0px; PADDING-TOP: =
0px; LIST-STYLE-TYPE: none
}
A.rellink:link {
	PADDING-RIGHT: 3px; DISPLAY: block; PADDING-LEFT: 3px; FONT-SIZE: =
x-small; PADDING-BOTTOM: 3px; COLOR: #033591; PADDING-TOP: 3px; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; TEXT-DECORATION: none
}
A.rellink:visited {
	PADDING-RIGHT: 3px; DISPLAY: block; PADDING-LEFT: 3px; FONT-SIZE: =
x-small; PADDING-BOTTOM: 3px; COLOR: #033591; PADDING-TOP: 3px; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; TEXT-DECORATION: none
}
A.rellink:active {
	PADDING-RIGHT: 3px; DISPLAY: block; PADDING-LEFT: 3px; FONT-SIZE: =
x-small; PADDING-BOTTOM: 3px; COLOR: #033591; PADDING-TOP: 3px; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; TEXT-DECORATION: none
}
A.rellink:hover {
	PADDING-RIGHT: 3px; DISPLAY: block; PADDING-LEFT: 3px; FONT-SIZE: =
x-small; PADDING-BOTTOM: 3px; COLOR: #333; PADDING-TOP: 3px; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; BACKGROUND-COLOR: =
#bfd3e5; TEXT-DECORATION: none
}
.rltext {
	PADDING-RIGHT: 3px; DISPLAY: block; PADDING-LEFT: 3px; FONT-SIZE: =
x-small; PADDING-BOTTOM: 3px; PADDING-TOP: 3px; FONT-FAMILY: arial, =
tahoma, verdana, sans-serif; TEXT-DECORATION: none
}
P.h3 {
	MARGIN-TOP: 0px; PADDING-LEFT: 0px; FONT-WEIGHT: bold; FONT-SIZE: 14px; =
MARGIN-BOTTOM: 8px; PADDING-BOTTOM: 2px; COLOR: #000000; PADDING-TOP: =
0px; FONT-FAMILY: Arial, Verdana, sans-serif
}
TABLE.contentContainer {
	BORDER-RIGHT: #555 1px solid; BORDER-LEFT: #555 1px solid; =
BORDER-BOTTOM: #555 1px solid; BACKGROUND-COLOR: #fff
}
TD.contentContainer {
	BORDER-RIGHT: #555 1px solid; BORDER-TOP: #555 1px solid; BORDER-LEFT: =
#555 1px solid; BORDER-BOTTOM: #555 1px solid; BACKGROUND-COLOR: #fff
}
.TableFMCSA {
	BORDER-RIGHT: #cacaca 1px solid; BORDER-TOP: #cacaca 1px solid; =
BORDER-LEFT: #cacaca 1px solid; BORDER-BOTTOM: #cacaca 1px solid
}
.TableTitleFMCSA {
	PADDING-LEFT: 5px; FONT-WEIGHT: bold; FONT-SIZE: small; PADDING-BOTTOM: =
5px; COLOR: #000000; PADDING-TOP: 10px; FONT-FAMILY: arial, verdana, =
sans-serif; TEXT-ALIGN: left
}
.LeftTableHeadFMCSA {
	BORDER-RIGHT: #ffffff 0px solid; BORDER-TOP: #ffffff 1px solid; =
PADDING-LEFT: 8px; FONT-WEIGHT: bold; FONT-SIZE: small; BORDER-LEFT: =
#ffffff 1px solid; BORDER-BOTTOM: #ffffff 1px solid; FONT-FAMILY: Arial, =
Verdana, sans-serif; BACKGROUND-COLOR: #c8d8ef; TEXT-ALIGN: left
}
.RightTableHeadFMCSA {
	BORDER-RIGHT: #ffffff 1px solid; BORDER-TOP: #ffffff 1px solid; =
PADDING-LEFT: 8px; FONT-WEIGHT: bold; FONT-SIZE: small; BORDER-LEFT: =
#ffffff 0px solid; BORDER-BOTTOM: #ffffff 1px solid; FONT-FAMILY: Arial, =
Verdana, sans-serif; BACKGROUND-COLOR: #c8d8ef; TEXT-ALIGN: left
}
.MiddleTableHeadFMCSA {
	BORDER-RIGHT: #ffffff 0px solid; PADDING-RIGHT: 2px; BORDER-TOP: =
#ffffff 1px solid; MARGIN-TOP: 1px; PADDING-LEFT: 8px; FONT-WEIGHT: =
bold; FONT-SIZE: small; PADDING-BOTTOM: 2px; BORDER-LEFT: #ffffff 0px =
solid; PADDING-TOP: 2px; BORDER-BOTTOM: #ffffff 1px solid; FONT-FAMILY: =
Arial, Verdana, sans-serif; BACKGROUND-COLOR: #c8d8ef; TEXT-ALIGN: left
}
.MiddleTDFMCSA {
	BORDER-RIGHT: #cacaca 0px solid; PADDING-RIGHT: 2px; BORDER-TOP: =
#cacaca 1px solid; MARGIN-TOP: 1px; PADDING-LEFT: 8px; FONT-SIZE: small; =
PADDING-BOTTOM: 2px; BORDER-LEFT: #cacaca 0px solid; PADDING-TOP: 2px; =
BORDER-BOTTOM: #cacaca 0px solid; FONT-FAMILY: Arial, Verdana, =
sans-serif; TEXT-ALIGN: left
}
.MiddleTDFMCSA_center {
	BORDER-RIGHT: #cacaca 0px solid; PADDING-RIGHT: 2px; BORDER-TOP: =
#cacaca 1px solid; MARGIN-TOP: 1px; PADDING-LEFT: 8px; FONT-SIZE: small; =
PADDING-BOTTOM: 2px; BORDER-LEFT: #cacaca 0px solid; PADDING-TOP: 2px; =
BORDER-BOTTOM: #cacaca 0px solid; FONT-FAMILY: Arial, Verdana, =
sans-serif; TEXT-ALIGN: center
}
.MiddleAltTDFMCSA {
	BORDER-RIGHT: #cacaca 0px solid; PADDING-RIGHT: 2px; BORDER-TOP: =
#cacaca 1px solid; MARGIN-TOP: 1px; PADDING-LEFT: 8px; FONT-SIZE: small; =
PADDING-BOTTOM: 2px; BORDER-LEFT: #cacaca 0px solid; PADDING-TOP: 2px; =
BORDER-BOTTOM: #cacaca 0px solid; FONT-FAMILY: Arial, Verdana, =
sans-serif; BACKGROUND-COLOR: #ececec; TEXT-ALIGN: left
}
.MiddleAltTDFMCSA_center {
	BORDER-RIGHT: #cacaca 0px solid; PADDING-RIGHT: 2px; BORDER-TOP: =
#cacaca 1px solid; MARGIN-TOP: 1px; PADDING-LEFT: 8px; FONT-SIZE: small; =
PADDING-BOTTOM: 2px; BORDER-LEFT: #cacaca 0px solid; PADDING-TOP: 2px; =
BORDER-BOTTOM: #cacaca 0px solid; FONT-FAMILY: Arial, Verdana, =
sans-serif; BACKGROUND-COLOR: #ececec; TEXT-ALIGN: center
}
A:link {
	FONT-SIZE: small; COLOR: #033591; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: none
}
A:visited {
	FONT-SIZE: small; COLOR: #033591; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: none
}
A:active {
	FONT-SIZE: small; COLOR: #033591; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: none
}
A:hover {
	FONT-SIZE: small; COLOR: #033591; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: underline
}
A.more:link {
	FONT-SIZE: x-small; COLOR: #033591; FONT-FAMILY: arial, tahoma, =
verdana, sans-serif; TEXT-DECORATION: none
}
A.more:visited {
	FONT-SIZE: x-small; COLOR: #033591; FONT-FAMILY: arial, tahoma, =
verdana, sans-serif; TEXT-DECORATION: none
}
A.more:active {
	FONT-SIZE: x-small; COLOR: #033591; FONT-FAMILY: arial, tahoma, =
verdana, sans-serif; TEXT-DECORATION: none
}
A.more:hover {
	FONT-SIZE: x-small; COLOR: #033591; FONT-FAMILY: arial, tahoma, =
verdana, sans-serif; TEXT-DECORATION: underline
}
A.printme {
	PADDING-RIGHT: 4px; FONT-WEIGHT: normal; FONT-SIZE: 10px; COLOR: =
#033591; MARGIN-RIGHT: 3px; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: none
}
A.printme:visited {
	PADDING-RIGHT: 4px; FONT-WEIGHT: normal; FONT-SIZE: 10px; COLOR: =
#033591; MARGIN-RIGHT: 3px; FONT-FAMILY: arial, tahoma, verdana, =
sans-serif; TEXT-DECORATION: none
}
A.printme:hover {
	TEXT-DECORATION: underline
}
A.h3:link {
	FONT-WEIGHT: normal; FONT-SIZE: 18px; MARGIN-BOTTOM: 0px; COLOR: =
#a0161d; FONT-FAMILY: georgia, times new roman, serif; TEXT-DECORATION: =
none
}
A.h3:visited {
	FONT-WEIGHT: normal; FONT-SIZE: 18px; MARGIN-BOTTOM: 0px; COLOR: =
#a0161d; FONT-FAMILY: georgia, times new roman, serif; TEXT-DECORATION: =
none
}
A.h3:active {
	FONT-WEIGHT: normal; FONT-SIZE: 18px; MARGIN-BOTTOM: 0px; COLOR: =
#a0161d; FONT-FAMILY: georgia, times new roman, serif; TEXT-DECORATION: =
none
}
A.h3:hover {
	FONT-WEIGHT: normal; FONT-SIZE: 18px; MARGIN-BOTTOM: 0px; COLOR: =
#a0161d; FONT-FAMILY: georgia, times new roman, serif; TEXT-DECORATION: =
underline
}
.sitemap {
	FONT-SIZE: small; FONT-FAMILY: arial, tahoma, verdana, sans-serif
}
H1 {
	MARGIN-TOP: 0px; FONT-WEIGHT: normal; FONT-SIZE: 22px; MARGIN-BOTTOM: =
0px; MARGIN-LEFT: 4px; COLOR: #a0161d; FONT-STYLE: italic; FONT-FAMILY: =
georgia, times new roman, serif
}
H2 {
	MARGIN-TOP: 0px; FONT-WEIGHT: normal; FONT-SIZE: 20px; MARGIN-BOTTOM: =
0px; MARGIN-LEFT: 4px; COLOR: #a0161d; FONT-STYLE: italic; FONT-FAMILY: =
georgia, times new roman, serif
}
H2.titleheadline {
	FONT-WEIGHT: normal; FONT-SIZE: 20px; MARGIN: 0px 4px; COLOR: #172c65; =
FONT-STYLE: italic; FONT-FAMILY: georgia, times new roman, serif
}
H3 {
	MARGIN-TOP: 0px; FONT-WEIGHT: normal; FONT-SIZE: 18px; MARGIN-BOTTOM: =
0px; COLOR: #a0161d; FONT-FAMILY: georgia, times new roman, serif
}
H3.subsection {
	MARGIN-TOP: 0px; FONT-WEIGHT: normal; FONT-SIZE: 16px; MARGIN-BOTTOM: =
0px; COLOR: #a0161d; FONT-FAMILY: georgia, times new roman, serif
}
H4 {
	MARGIN-TOP: 0px; FONT-WEIGHT: normal; FONT-SIZE: 16px; MARGIN-BOTTOM: =
0px; COLOR: #a0161d; FONT-FAMILY: georgia, times new roman, serif
}
CAPTION {
	MARGIN-TOP: 0px; FONT-WEIGHT: normal; FONT-SIZE: 16px; MARGIN-BOTTOM: =
0px; COLOR: #a0161d; FONT-FAMILY: georgia, times new roman, serif
}
H6.date {
	MARGIN-TOP: 3px; FONT-WEIGHT: normal; FONT-SIZE: 11px; MARGIN-BOTTOM: =
4px; COLOR: #3a4356; FONT-STYLE: italic; FONT-FAMILY: arial, tahoma, =
verdana, sans-serif
}
TD.headline {
	BORDER-BOTTOM: #555 1px solid
}
TD.titleheadline {
	BORDER-BOTTOM: #a0161d 2px solid
}
FORM {
	MARGIN-TOP: 0px; MARGIN-BOTTOM: 0px; PADDING-BOTTOM: 0px; PADDING-TOP: =
0px
}
INPUT.reg_text_search {
	WIDTH: 110px
}
INPUT.text {
	BORDER-RIGHT: 2px inset; BORDER-TOP: 2px inset; FONT-SIZE: 8pt; =
BORDER-LEFT: 2px inset; BORDER-BOTTOM: 2px inset; FONT-FAMILY: arial, =
tahoma, verdana, sans-serif
}
INPUT.radio {
	BORDER-RIGHT: 2px; BORDER-TOP: 2px; FONT-SIZE: 8pt; BORDER-LEFT: 2px; =
BORDER-BOTTOM: 2px; FONT-FAMILY: arial, tahoma, verdana, sans-serif
}
INPUT.checkbox {
	BORDER-RIGHT: 2px; BORDER-TOP: 2px; FONT-SIZE: 8pt; BORDER-LEFT: 2px; =
BORDER-BOTTOM: 2px; FONT-FAMILY: arial, tahoma, verdana, sans-serif
}
SELECT {
	BORDER-RIGHT: 2px inset; BORDER-TOP: 2px inset; FONT-SIZE: 8pt; =
BORDER-LEFT: 2px inset; BORDER-BOTTOM: 2px inset; FONT-FAMILY: arial, =
tahoma, verdana, sans-serif
}
INPUT.button {
	FONT-WEIGHT: bold; FONT-SIZE: 12px; COLOR: #ffffff; BORDER-TOP-STYLE: =
outset; FONT-FAMILY: Arial, Verdana, sans-serif; BORDER-RIGHT-STYLE: =
outset; BORDER-LEFT-STYLE: outset; HEIGHT: 22px; BACKGROUND-COLOR: =
#454c5d; BORDER-BOTTOM-STYLE: outset
}
TEXTAREA {
	BORDER-RIGHT: 2px inset; BORDER-TOP: 2px inset; FONT-SIZE: 8pt; =
BORDER-LEFT: 2px inset; WIDTH: 300px; BORDER-BOTTOM: 2px inset; =
FONT-FAMILY: arial, tahoma, verdana, sans-serif; HEIGHT: 100px
}
* {
=09
}
.clsTopPadd {
	BORDER-TOP: #555 1px solid; PADDING-TOP: 0px; BORDER-BOTTOM: #555 1px =
solid; BACKGROUND-COLOR: #172b65
}
IMG.bdr {
	BORDER-RIGHT: #555 1px solid; BORDER-TOP: #555 1px solid; BORDER-LEFT: =
#555 1px solid; BORDER-BOTTOM: #555 1px solid
}
LI.main {
=09
}
LI.move {
	BORDER-BOTTOM: #555 1px solid
}
LI.title {
	FONT-WEIGHT: bold
}
LI.hdr {
	BORDER-RIGHT: #555 1px solid; BORDER-TOP: #555 1px solid; PADDING-LEFT: =
4px; FONT-WEIGHT: bold; FONT-SIZE: 12px; BORDER-LEFT: #555 1px solid; =
COLOR: #444; BORDER-BOTTOM: #555 1px solid; FONT-FAMILY: arial, tahoma, =
verdana, sans-serif; BACKGROUND-COLOR: #eaeaea
}
.newsspan {
	PADDING-LEFT: 3px
}
.relatedbg {
	BORDER-RIGHT: #555 1px solid; BORDER-TOP: #555 1px solid; MARGIN-TOP: =
3px; BORDER-LEFT: #555 1px solid; BORDER-BOTTOM: #555 1px solid; =
BACKGROUND-COLOR: #ddd
}
.righttableBG {
	MARGIN-TOP: 0px; FONT-WEIGHT: normal; FONT-SIZE: 18px; MARGIN-BOTTOM: =
0px; COLOR: #a0161d; BORDER-BOTTOM: #555 1px solid; FONT-FAMILY: =
georgia, times new roman, serif
}
.righttableBG A:link {
	MARGIN-TOP: 0px; FONT-WEIGHT: normal; FONT-SIZE: 18px; MARGIN-BOTTOM: =
0px; COLOR: #a0161d; BORDER-BOTTOM: #555 1px solid; FONT-FAMILY: =
georgia, times new roman, serif
}
.righttableBG A:visited {
	MARGIN-TOP: 0px; FONT-WEIGHT: normal; FONT-SIZE: 18px; MARGIN-BOTTOM: =
0px; COLOR: #a0161d; BORDER-BOTTOM: #555 1px solid; FONT-FAMILY: =
georgia, times new roman, serif
}
TABLE.searching {
	MARGIN-LEFT: 10px
}
TD.bdrRight {
	BORDER-RIGHT: #555 1px solid
}
TD.header {
	BORDER-RIGHT: #555 1px solid; BORDER-TOP: #555 1px solid; MARGIN-TOP: =
0px; BACKGROUND-COLOR: #fff
}
TD.righttableRLBorder {
	BORDER-RIGHT: #555 1px solid; BORDER-LEFT: #555 1px solid; =
BORDER-BOTTOM: #555 1px solid
}
TD.righttableRLBorder UL {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: 0px; MARGIN: =
2px; PADDING-TOP: 0px; LIST-STYLE-TYPE: none
}
#qs {
	FONT-SIZE: 10px; WIDTH: 150px; COLOR: #333; FONT-FAMILY: arial, tahoma, =
verdana, sans-serif
}
.yellowstrip {
	PADDING-RIGHT: 5px; BORDER-TOP: #172b65 1px solid; PADDING-LEFT: 12px; =
FONT-SIZE: 12px; PADDING-BOTTOM: 5px; PADDING-TOP: 5px; BORDER-BOTTOM: =
#172b65 1px solid; FONT-FAMILY: arial, verdana, sans-serif; =
BACKGROUND-COLOR: #f9f8e3
}
.pubdate {
	PADDING-LEFT: 0px; FONT-SIZE: 11px; PADDING-TOP: 6px; FONT-STYLE: =
italic; FONT-FAMILY: Arial, Helvetica, sans-serif
}
TD.progress {
	BACKGROUND-POSITION: right top; BACKGROUND-IMAGE: =
url(/images/gettingstarted/right_cap_wt.gif); BACKGROUND-REPEAT: =
no-repeat; HEIGHT: 16px
}
TD.progressSome {
	BACKGROUND-POSITION: right top; BACKGROUND-IMAGE: =
url(/images/gettingstarted/right_cap.gif); BACKGROUND-REPEAT: no-repeat; =
HEIGHT: 16px
}
TABLE.progressbar {
	BORDER-RIGHT: #555 1px solid; BORDER-LEFT: #555 1px solid; =
BACKGROUND-COLOR: #eaeaea
}
.sectionButton {
	BORDER-RIGHT: #bd5c61 1px solid; PADDING-RIGHT: 5px; BORDER-TOP: =
#bd5c61 1px solid; PADDING-LEFT: 5px; FONT-WEIGHT: bold; FONT-SIZE: =
12px; PADDING-BOTTOM: 5px; BORDER-LEFT: #bd5c61 1px solid; COLOR: =
#172c65; PADDING-TOP: 5px; BORDER-BOTTOM: #bd5c61 1px solid; =
FONT-FAMILY: Georgia; BACKGROUND-COLOR: #dde5ec; TEXT-ALIGN: center
}
TABLE.rhtable TD.spacing {
=09
}
#rhtableHEAD {
=09
}
#rhtableHEAD A:link {
	FONT-WEIGHT: bold; FONT-SIZE: 12px; BACKGROUND-IMAGE: =
url(/images/new_style/btnback.jpg); FONT-FAMILY: Georgia
}
.rhtableHEAD A:visited {
	FONT-WEIGHT: bold; FONT-SIZE: 12px; BACKGROUND-IMAGE: =
url(/images/new_style/btnback.jpg); FONT-FAMILY: Georgia
}
.pH2 {
	FONT-SIZE: smaller; COLOR: #993300; FONT-FAMILY: Arial, Helvetica, =
sans-serif; TEXT-ALIGN: center
}
TABLE#tblSafety {
	BORDER-TOP-WIDTH: thin; PADDING-RIGHT: 0px; BACKGROUND-POSITION: center =
50%; PADDING-LEFT: 0px; BORDER-LEFT-WIDTH: thin; BORDER-LEFT-COLOR: =
#6633ff; BORDER-BOTTOM-WIDTH: thin; BORDER-BOTTOM-COLOR: #6633ff; =
PADDING-BOTTOM: 0px; BORDER-TOP-COLOR: #6633ff; PADDING-TOP: 0px; =
FONT-FAMILY: Arial, Helvetica, sans-serif; BORDER-RIGHT-WIDTH: thin; =
BORDER-RIGHT-COLOR: #6633ff
}
.tblSafetySub {
	FONT-SIZE: larger; COLOR: #ffffff; FONT-FAMILY: Arial, Helvetica, =
sans-serif; BACKGROUND-COLOR: #3333ff; TEXT-ALIGN: center
}
.tblSafetySub1 {
	FONT-WEIGHT: normal; FONT-FAMILY: Arial, Helvetica, sans-serif; =
TEXT-ALIGN: left
}
.tblSafetyData {
	VERTICAL-ALIGN: middle; FONT-FAMILY: Arial, Helvetica, sans-serif; =
TEXT-ALIGN: center
}
TABLE#tblQuality {
	BORDER-TOP-WIDTH: thin; PADDING-RIGHT: 1px; BACKGROUND-POSITION: center =
50%; PADDING-LEFT: 1px; BORDER-LEFT-WIDTH: thin; BORDER-LEFT-COLOR: =
#993300; BORDER-BOTTOM-WIDTH: thin; BORDER-BOTTOM-COLOR: #993300; =
PADDING-BOTTOM: 1px; BORDER-TOP-COLOR: #993300; PADDING-TOP: 1px; =
FONT-FAMILY: Arial, Helvetica, sans-serif; BORDER-RIGHT-WIDTH: thin; =
BORDER-RIGHT-COLOR: #993300
}
.tblQualitySub {
	FONT-SIZE: larger; BORDER-LEFT-COLOR: #ffffff; BORDER-BOTTOM-COLOR: =
#ffffff; COLOR: #ffffff; BORDER-TOP-COLOR: #ffffff; FONT-FAMILY: Arial, =
Helvetica, sans-serif; BACKGROUND-COLOR: #993300; TEXT-ALIGN: center; =
BORDER-RIGHT-COLOR: #ffffff
}
.tblQualitySubH {
	BACKGROUND-COLOR: #ffcc99; TEXT-ALIGN: left
}
.tblQualitySub1 {
	FONT-WEIGHT: normal; FONT-FAMILY: Arial, Helvetica, sans-serif; =
TEXT-ALIGN: left
}
.tblQualityData {
	VERTICAL-ALIGN: middle; FONT-FAMILY: Arial, Helvetica, sans-serif; =
TEXT-ALIGN: center
}
TABLE#tblProgram {
	BORDER-TOP-WIDTH: thin; PADDING-RIGHT: 1px; BACKGROUND-POSITION: center =
50%; PADDING-LEFT: 1px; BORDER-LEFT-WIDTH: thin; BORDER-LEFT-COLOR: =
#6633ff; BORDER-BOTTOM-WIDTH: thin; BORDER-BOTTOM-COLOR: #6633ff; =
PADDING-BOTTOM: 1px; BORDER-TOP-COLOR: #6633ff; PADDING-TOP: 1px; =
FONT-FAMILY: Arial, Helvetica, sans-serif; BORDER-RIGHT-WIDTH: thin; =
BORDER-RIGHT-COLOR: #6633ff
}
.tblProgramSub {
	FONT-SIZE: larger; BORDER-LEFT-COLOR: #ffffff; BORDER-BOTTOM-COLOR: =
#ffffff; COLOR: #ffffff; BORDER-TOP-COLOR: #ffffff; FONT-FAMILY: Arial, =
Helvetica, sans-serif; BACKGROUND-COLOR: #3333ff; TEXT-ALIGN: center; =
BORDER-RIGHT-COLOR: #ffffff
}
.tblProgramSubH {
	BACKGROUND-COLOR: #33ccff; TEXT-ALIGN: left
}
.tblProgramSub1 {
	FONT-WEIGHT: normal; FONT-FAMILY: Arial, Helvetica, sans-serif; =
TEXT-ALIGN: left
}
.tblProgramData {
	VERTICAL-ALIGN: middle; FONT-FAMILY: Arial, Helvetica, sans-serif; =
TEXT-ALIGN: center
}
.tdNote {
	FONT-SIZE: smaller; FONT-STYLE: italic; TEXT-ALIGN: left
}
#references {
	LIST-STYLE-POSITION: outside
}
#references LI {
	PADDING-RIGHT: 15px; PADDING-BOTTOM: 5px; MARGIN-LEFT: 1.25em; =
PADDING-TOP: 5px
}
.pgNav {
	TEXT-ALIGN: right
}
.pgNav A:link {
=09
}
.SpanTimer {
	FONT-SIZE: 30px; VERTICAL-ALIGN: middle; COLOR: navy; BACKGROUND-COLOR: =
gray
}
.quicknav {
	BORDER-RIGHT: #e6e6e6 0.15em dashed; BORDER-TOP: #e6e6e6 0.15em dashed; =
FONT-SIZE: 10px; BORDER-LEFT: #e6e6e6 0.15em dashed; BORDER-BOTTOM: =
#e6e6e6 0.15em dashed; font-size-adjust: inherit
}
.quicknav A:link {
	FONT-SIZE: 10px
}
.quicknav A:visited {
	FONT-SIZE: 10px
}
.odd {
	BORDER-RIGHT: #aaaaaa thin solid; BORDER-TOP: #aaaaaa thin solid; =
BORDER-LEFT: #aaaaaa thin solid; BORDER-BOTTOM: #aaaaaa thin solid; =
BACKGROUND-COLOR: #defee9
}
.odd H2 {
	MARGIN-TOP: 0px; PADDING-LEFT: 25px; FONT-SIZE: medium; PADDING-BOTTOM: =
5px; COLOR: #ffffff; PADDING-TOP: 5px; BACKGROUND-COLOR: #0033cc
}
.even {
	BORDER-RIGHT: #aaaaaa thin solid; BORDER-TOP: #aaaaaa thin solid; =
BORDER-LEFT: #aaaaaa thin solid; BORDER-BOTTOM: #aaaaaa thin solid; =
BACKGROUND-COLOR: #ffffff
}
.heading2 {
	MARGIN-TOP: 0px; PADDING-LEFT: 25px; FONT-WEIGHT: bold; FONT-SIZE: =
larger; PADDING-BOTTOM: 5px; COLOR: #ffffff; PADDING-TOP: 5px; =
FONT-STYLE: normal; BACKGROUND-COLOR: #0033cc
}

------=_NextPart_000_0000_01CA4DB9.92AFE550
Content-Type: text/css;
	charset="iso-8859-1"
Content-Transfer-Encoding: quoted-printable
Content-Location: http://www.fmcsa.dot.gov/rules-regulations/administration/fmcsr/mcregis4/XMLPages/Content.css

H3 {
	MARGIN-TOP: 12pt; DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 16px; =
MARGIN-BOTTOM: 6pt; TEXT-ALIGN: left
}
H4 {
	MARGIN-TOP: 12pt; DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 16px; =
MARGIN-BOTTOM: 6pt; FONT-STYLE: italic; TEXT-ALIGN: left
}
H5 {
	MARGIN-TOP: 12pt; DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; =
MARGIN-BOTTOM: 6pt; FONT-STYLE: normal; TEXT-ALIGN: left
}
H6 {
	MARGIN-TOP: 12pt; DISPLAY: block; FONT-WEIGHT: bold; FONT-SIZE: 12px; =
MARGIN-BOTTOM: 6pt; FONT-STYLE: italic; TEXT-ALIGN: left
}
CAPTION {
	TEXT-ALIGN: center
}
TABLE.standard {
	BORDER-TOP-STYLE: solid; BORDER-RIGHT-STYLE: solid; BORDER-LEFT-STYLE: =
solid; BORDER-BOTTOM-STYLE: solid
}
TABLE.bx {
	BORDER-TOP-STYLE: solid; BORDER-RIGHT-STYLE: solid; BORDER-LEFT-STYLE: =
solid; BORDER-BOTTOM-STYLE: solid
}
TABLE.state {
	BORDER-TOP-STYLE: solid; BORDER-RIGHT-STYLE: solid; BORDER-LEFT-STYLE: =
solid; BORDER-BOTTOM-STYLE: solid
}
TABLE.nr {
	BORDER-TOP-STYLE: solid; BORDER-RIGHT-STYLE: solid; BORDER-LEFT-STYLE: =
solid; BORDER-BOTTOM-STYLE: solid
}
TABLE.nrbody {
	BORDER-TOP-STYLE: solid; BORDER-RIGHT-STYLE: solid; BORDER-LEFT-STYLE: =
solid; BORDER-BOTTOM-STYLE: solid
}
TABLE.nrhrhd {
	BORDER-TOP-STYLE: solid; BORDER-RIGHT-STYLE: solid; BORDER-LEFT-STYLE: =
solid; BORDER-BOTTOM-STYLE: solid
}
TABLE.nrlist {
	BORDER-TOP-STYLE: solid; BORDER-RIGHT-STYLE: solid; BORDER-LEFT-STYLE: =
solid; BORDER-BOTTOM-STYLE: solid
}
TABLE.nrbody {
	FONT-SIZE: 100%
}
TABLE.vrbody {
	FONT-SIZE: 100%
}
TABLE.nrlist {
	FONT-SIZE: 100%
}
TABLE.standard {
	FONT-FAMILY: Helvetica
}
TABLE.bx {
	FONT-FAMILY: Helvetica
}
TABLE.nr {
	FONT-FAMILY: Helvetica
}
TABLE.nrbody {
	FONT-FAMILY: Helvetica
}
TABLE.nrhrhd {
	FONT-FAMILY: Helvetica
}
TABLE.nrbdhrhd {
	FONT-FAMILY: Helvetica
}
TABLE.vr {
	FONT-FAMILY: Helvetica
}
TABLE.vrhr {
	FONT-FAMILY: Helvetica
}
TABLE.state {
	FONT-FAMILY: Helvetica
}
TABLE.standard {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
MARGIN-TOP: 6pt; FONT-SIZE: 90%; MARGIN-BOTTOM: 6pt; BORDER-LEFT: black =
thin solid; BORDER-BOTTOM: black thin solid; BORDER-COLLAPSE: collapse
}
TABLE.bx {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
MARGIN-TOP: 6pt; FONT-SIZE: 90%; MARGIN-BOTTOM: 6pt; BORDER-LEFT: black =
thin solid; BORDER-BOTTOM: black thin solid; BORDER-COLLAPSE: collapse
}
TABLE.nr {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
MARGIN-TOP: 6pt; FONT-SIZE: 90%; MARGIN-BOTTOM: 6pt; BORDER-LEFT: black =
thin solid; BORDER-BOTTOM: black thin solid; BORDER-COLLAPSE: collapse
}
TABLE.nrbody {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
MARGIN-TOP: 6pt; FONT-SIZE: 90%; MARGIN-BOTTOM: 6pt; BORDER-LEFT: black =
thin solid; BORDER-BOTTOM: black thin solid; BORDER-COLLAPSE: collapse
}
TABLE.nrhrhd {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
MARGIN-TOP: 6pt; FONT-SIZE: 90%; MARGIN-BOTTOM: 6pt; BORDER-LEFT: black =
thin solid; BORDER-BOTTOM: black thin solid; BORDER-COLLAPSE: collapse
}
TABLE.nrbdhrhd {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
MARGIN-TOP: 6pt; FONT-SIZE: 90%; MARGIN-BOTTOM: 6pt; BORDER-LEFT: black =
thin solid; BORDER-BOTTOM: black thin solid; BORDER-COLLAPSE: collapse
}
TABLE.vr {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
MARGIN-TOP: 6pt; FONT-SIZE: 90%; MARGIN-BOTTOM: 6pt; BORDER-LEFT: black =
thin solid; BORDER-BOTTOM: black thin solid; BORDER-COLLAPSE: collapse
}
TABLE.vrhr {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
MARGIN-TOP: 6pt; FONT-SIZE: 90%; MARGIN-BOTTOM: 6pt; BORDER-LEFT: black =
thin solid; BORDER-BOTTOM: black thin solid; BORDER-COLLAPSE: collapse
}
TABLE.state {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
MARGIN-TOP: 6pt; FONT-SIZE: 90%; MARGIN-BOTTOM: 6pt; BORDER-LEFT: black =
thin solid; BORDER-BOTTOM: black thin solid; BORDER-COLLAPSE: collapse
}
TABLE.nrlist {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
MARGIN-TOP: 6pt; FONT-SIZE: 90%; MARGIN-BOTTOM: 6pt; BORDER-LEFT: black =
thin solid; BORDER-BOTTOM: black thin solid; BORDER-COLLAPSE: collapse
}
TH.bx {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TD.bx {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TH.hr {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TD.hr {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TH.nr {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TD.nr {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TH.nrbody {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TD.nrbody {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TH.nrhrhd {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TD.nrhrhd {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TH.nrbdhrhd {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TD.nrbdhrhd {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TH.standard {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TD.standard {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TH.vr {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TD.vr {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TH.nrlist {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TD.nrlist {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TD.vrhr {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TH.vrhr {
	BORDER-RIGHT: black thin solid; BORDER-TOP: black thin solid; =
BORDER-LEFT: black thin solid; BORDER-BOTTOM: black thin solid
}
TH.bx {
	BORDER-RIGHT-STYLE: solid
}
TD.bx {
	BORDER-RIGHT-STYLE: solid
}
TH.hr {
	BORDER-RIGHT-STYLE: solid
}
TD.hr {
	BORDER-RIGHT-STYLE: solid
}
TH.nr {
	BORDER-RIGHT-STYLE: solid
}
TD.nr {
	BORDER-RIGHT-STYLE: solid
}
TH.nrbody {
	BORDER-RIGHT-STYLE: solid
}
TD.nrbody {
	BORDER-RIGHT-STYLE: solid
}
TH.nrhrhd {
	BORDER-RIGHT-STYLE: solid
}
TD.nrhrhd {
	BORDER-RIGHT-STYLE: solid
}
TH.nrbdhrhd {
	BORDER-RIGHT-STYLE: solid
}
TD.nrbdhrhd {
	BORDER-RIGHT-STYLE: solid
}
TH.bx {
	BORDER-BOTTOM-STYLE: solid
}
TH.nr {
	BORDER-BOTTOM-STYLE: solid
}
TH.nrbody {
	BORDER-BOTTOM-STYLE: solid
}
TD.bx {
	BORDER-BOTTOM-STYLE: solid
}
TD.nr {
	BORDER-BOTTOM-STYLE: solid
}
TD.nrbody {
	BORDER-BOTTOM-STYLE: solid
}
TD.nrhrhd {
	BORDER-BOTTOM-STYLE: solid
}
TD.nrbdhrhd {
	BORDER-BOTTOM-STYLE: solid
}
TD.vr {
	BORDER-BOTTOM-STYLE: solid
}
TD.state {
	BORDER-BOTTOM-STYLE: solid
}
TD.vrbody {
	BORDER-BOTTOM-STYLE: solid
}
TD.ftrstandard {
	BORDER-TOP-STYLE: solid
}
TD.ftrhr {
	BORDER-TOP-STYLE: solid
}
TD.ftrnrhrhd {
	BORDER-TOP-STYLE: solid
}
TD.ftrnrbdhrhd {
	BORDER-TOP-STYLE: solid
}
TD.ftrvr {
	BORDER-TOP-STYLE: solid
}
TD.ftrvrhr {
	BORDER-TOP-STYLE: solid
}
TD.ftrstate {
	BORDER-TOP-STYLE: solid
}
TD.ftrvrbody {
	BORDER-TOP-STYLE: solid
}
TD.ftrbx {
	BORDER-TOP-STYLE: none; BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: =
none; BORDER-BOTTOM-STYLE: none
}
TD.ftrnr {
	BORDER-TOP-STYLE: none; BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: =
none; BORDER-BOTTOM-STYLE: none
}
TD.ftrnrbody {
	BORDER-TOP-STYLE: none; BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: =
none; BORDER-BOTTOM-STYLE: none
}
TD.HMTheader {
	FONT-WEIGHT: bold; COLOR: black; BORDER-TOP-STYLE: none; =
BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; BACKGROUND-COLOR: =
rgb(214,199,163); BORDER-BOTTOM-STYLE: none
}
TD.HMTitem {
	COLOR: black; BORDER-RIGHT-STYLE: none; BORDER-BOTTOM-STYLE: solid
}
TD.HMTdetail {
	COLOR: black; BORDER-TOP-STYLE: none; PADDING-TOP: 0pt; =
BORDER-RIGHT-STYLE: none; BORDER-BOTTOM-STYLE: none
}
TD.HMTdetailPad {
	COLOR: black; BORDER-TOP-STYLE: none; PADDING-TOP: 12pt; =
BORDER-RIGHT-STYLE: none; BORDER-BOTTOM-STYLE: none
}

------=_NextPart_000_0000_01CA4DB9.92AFE550
Content-Type: application/octet-stream
Content-Transfer-Encoding: quoted-printable
Content-Location: http://www.fmcsa.dot.gov/include/javascript.js

/*
	This is the javascript file for the FMCSA Web site
*/
=20
/* Printing Functions */
		var oPrintWindow =3D null;
		function Print(sPage)
		{
			if (oPrintWindow !=3D null && oPrintWindow.closed =3D=3D false)
				oPrintWindow.close();
			var oPrintWindow =3D window.open("/print.asp?URL=3D"+sPage, =
"fmcsa_PrintWindow", "height=3D680, width=3D680, resizable=3Dyes, =
dependent=3Dyes, scrollbars=3Dyes");
		}

		var js_url

		function print_appendix(sPage) {

				window.open(sPage , 'fmcsa_PrintWindow', 'height=3D680, width=3D680, =
resizable=3Dyes, dependent=3Dyes, scrollbars=3Dyes')

		}

		function PrintURL(sPage)
		{
			if (oPrintWindow !=3D null && oPrintWindow.closed =3D=3D false)
				oPrintWindow.close();
			var oPrintWindow =3D window.open(sPage, "fmcsa_PrintWindow", =
"height=3D680, width=3D800, resizable=3Dyes, dependent=3Dyes, =
scrollbars=3Dyes");
		}

/* Formatting Functions */

		function fixTD()
		{
			var theight =3D -1;
			for (var i=3D0; i < document.all.length; i++) {
			=09
				if(document.all.item(i).id.toString() =3D=3D "TOCTop") {
					theight =3D document.all.item(i).offsetHeight;
				}=20
			=09
				if(document.all.item(i).id.toString() =3D=3D "toc1") {
					document.all.item(i).height =3D theight - =
document.all.item(i).offsetTop-5;
				}
			}		=09
        }


/* Created for Cross Border section */

			function MM_jumpMenu(targ,selObj,restore){ //v3.0
			  //alert(selObj.options[selObj.selectedIndex].value);
			  document.location.href =3D =
selObj.options[selObj.selectedIndex].value;
			  =
//eval(targ+".location=3D'"+selObj.options[selObj.selectedIndex].value+"'=
");
			  if (restore) selObj.selectedIndex=3D0;
			}

/* Search Functions */

		function clearSearch()
		{
			var txtSearch ;
			var txtSearch=3Ddocument.getElementById("q");
		=09
			txtSearch.value=3DtxtSearch.value.replace(/^ */, "").replace(/ *$/, =
"");
			//if (txtSearch.value.toLowerCase() =3D=3D "Search This =
Site".toLowerCase())
				txtSearch.value =3D "";
        }

		function fillSearch()
		{
			var txtSearch ;
			var txtSearch=3Ddocument.getElementById("q");
		=09
			txtSearch.value=3DtxtSearch.value.replace(/^ */, "").replace(/ *$/, =
"");
			if (txtSearch.value.toLowerCase() =3D=3D "".toLowerCase())
				txtSearch.value =3D "Search All FMCSA Sites";
        }
       =20
/* Date Format */

		function print_date(){
			var cur_date =3D new Date();
   			var hours =3D cur_date.getHours();
   			hours -=3D 5;
			cur_date.setHours(hours);
			var monthName =3D new Array("January", "February", "March", "April",
   			 							"May", "June", "July", "August", "September",
   										"October", "November", "December");
			var cur_month=3Dcur_date.getMonth();
			cur_day =3D cur_date.getDate();
			var suffix =3D "th";
			if ((cur_day =3D=3D 1) || (cur_day =3D=3D 21) || (cur_day =3D=3D 31)) =
{
				suffix =3D "st"
			} else if ((cur_day =3D=3D 2)  || (cur_day =3D=3D 22)) {
					suffix =3D "nd"
					}
					else if ((cur_day =3D=3D 3) || (cur_day =3D=3D 23)) {
						suffix =3D "rd"
						}
			suffix =3D "";
			/* var GMTtime =3D new Date();*/
			document.write(monthName[cur_month] + '&#160;' + cur_day + suffix + =
',&#160;' + cur_date.getFullYear());
			/* document.write ('<br/>' + GMTtime.getMonth() + '&#160;' + =
GMTtime.getDate() + '&#160;' + GMTtime.getFullYear() + '&#160;' + =
GMTtime.getHours()) */
		}       =20
	=09
function search_rules() {
	document.google.Google_URL.value =3D =
"http://google2.dot.gov/search?q=3Dsite:www.fmcsa.dot.gov/rules-regulatio=
ns/administration/fmcsr/%20" + document.google.q1.value + =
"&num=3D1000&btnG=3DSearch&site=3DDOT_Pages&output=3Dxml&client=3Ddefault=
_frontend&filter=3D0";
	document.google.submit();
	 return true;
   }
  =20
		 var myWindow =3D''  =20
       function popupwin(sURL) {
		//alert('hello')
        if (myWindow.location && !myWindow.closed)
        {
            myWindow.location.href =3D sURL;
            myWindow.focus();
        }
        else
        {
    	    myWindow =3D window.open(sURL, 'myWindow', =
'width=3D550,height=3D550,left=3D150,top=3D150,scrollbars=3Dyes,resizable=
=3Dyes,toolbar=3Dno,location=3Dno');
	        myWindow.focus();
		=09
	    }
    }


function resizeOuterTo(w,h) {
 if (parseInt(navigator.appVersion)>3) {
   if (navigator.appName=3D=3D"Netscape") {
    top.outerWidth=3Dw;
    top.outerHeight=3Dh;
   }
   else top.resizeTo(w,h);
 }
}


function launch(){
    if ((screen.width >1024)&&(screen.height >768)){
        nw =3D window.open("course/s1.asp","main",'TITLE =3D "No FEAR =
Act =
Awareness",width=3D770,height=3D590,left=3D100,top=3D50,scrollbars=3Dyes,=
toolbar=3Dno,location=3Dno,status=3Dyes');
		nw.resizeTo(780,640);
		 nw.focus();
    }
=20
else if ((screen.width <=3D1024)&&(screen.height <=3D768)){
      nw =3D =
window.open("course/s1.asp","main",'width=3D770,height=3D590,left=3D100,t=
op=3D50,scrollbars=3Dyes,toolbar=3Dno,location=3Dno,status=3Dyes');
	  nw.resizeTo(780,640);
	nw.focus();	 =20
     }
}

function launchtraining()=20
{
    if ((screen.width >1024)&&(screen.height >768))
      {
	 =20
        nw =3D window.open("course/s1.asp","main",'TITLE =3D "FMCSA IT =
Security Awareness =
Quiz",width=3D770,height=3D590,left=3D100,top=3D50,scrollbars=3Dyes,toolb=
ar=3Dno,location=3Dno,status=3Dyes');
		nw.resizeTo(780,640);
		 nw.focus();
       }
=20
else if ((screen.width <=3D1024)&&(screen.height <=3D768))


     {
      nw =3D =
window.open("course/s1.asp","main",'width=3D770,height=3D590,left=3D100,t=
op=3D50,scrollbars=3Dyes,toolbar=3Dno,location=3Dno,status=3Dyes');
	  nw.resizeTo(780,640);
    	nw.focus();	 =20
      }
  =20
}

function launchControltraining()=20
{
    if ((screen.width >1024)&&(screen.height >768))
      {
	 =20
        nw =3D window.open("course/s1.asp","Controlmain",'TITLE =3D =
"FMCSA Internal Control Awareness Course and =
Quiz",width=3D770,height=3D590,left=3D100,top=3D50,scrollbars=3Dyes,toolb=
ar=3Dno,location=3Dno,status=3Dyes');
		nw.resizeTo(780,640);
		 nw.focus();
       }
=20
else if ((screen.width <=3D1024)&&(screen.height <=3D768))


     {
      nw =3D =
window.open("course/s1.asp","Controlmain",'width=3D770,height=3D590,left=3D=
100,top=3D50,scrollbars=3Dyes,toolbar=3Dno,location=3Dno,status=3Dyes');
	  nw.resizeTo(780,640);
    	nw.focus();	 =20
      }
  =20
}

function cmmssst_validate(){
//	alert("hi");

 	with(window.document.contact){=20
		  	name.value=3Dname.value.replace(/^ */, "").replace(/ *$/, "")
		 	if(name.value=3D=3D"") {
				alert("You must enter name")
				name.focus();
				return false
			}
		=09
		  	company.value=3Dcompany.value.replace(/^ */, "").replace(/ *$/, "")
		 	if(company.value=3D=3D"") {
				alert("You must enter company name")
				company.focus();
				return false
			}
		  	email.value=3Demail.value.replace(/^ */, "").replace(/ *$/, "")
		 	if(email.value=3D=3D"") {
				alert("You must enter your email")
				email.focus();
				return false
			}=09
			return true;				=09
	}
}

/* getting started test resize script added for wizard's 508 compliance =
*/

//      Document Text Sizer- Copyright 2003 - Taewook Kang.  All rights =
reserved.
//	Web Site: http://txkang.com
//      Script featured on Dynamic Drive (http://www.dynamicdrive.com)
//      Specify affected tags. Add or remove from list:    =20
var tgs =3D new Array( 'div','td','tr','span','p', 'li', 'ul', 'ol', =
'a', 'body', 'form','input', 'img');

//Specify spectrum of different font sizes:

var szs =3D new Array( =
'xx-small','x-small','small','medium','large','x-large', 'xx-large' );
var startSz =3D 3;

function ts( trgt,inc ) {
	if (!document.getElementById) return
	var d =3D document,cEl =3D null,sz =3D startSz,i,j,cTags;
	sz +=3D inc;
	if ( sz < 0 ) sz =3D 0;
	if ( sz > 6 ) sz =3D 6;
	startSz =3D sz;
	if ( !( cEl =3D d.getElementById( trgt ) ) ) cEl =3D =
d.getElementsByTagName( trgt )[ 0 ];
	cEl.style.fontSize =3D szs[ sz ];
	for ( i =3D 0 ; i < tgs.length ; i++ ) {
		cTags =3D cEl.getElementsByTagName( tgs[ i ] );
		for ( j =3D 0 ; j < cTags.length ; j++ ) cTags[ j ].style.fontSize =3D =
szs[ sz ];
	}=09
=09
}





/**
 * DHTML email validation script. Courtesy of SmartWebby.com =
(http://www.smartwebby.com/dhtml/)
 */

function echeck(str) {

		var at=3D"@"
		var dot=3D"."
		var lat=3Dstr.indexOf(at)
		var lstr=3Dstr.length
		var ldot=3Dstr.indexOf(dot)
		if (str.indexOf(at)=3D=3D-1){
		   alert("invalid email")
		   return false
		}

		if (str.indexOf(at)=3D=3D-1 || str.indexOf(at)=3D=3D0 || =
str.indexOf(at)=3D=3Dlstr){
		   alert("invalid email")
		   return false
		}

		if (str.indexOf(dot)=3D=3D-1 || str.indexOf(dot)=3D=3D0 || =
str.indexOf(dot)=3D=3Dlstr){
		    alert("invalid email")
		    return false
		}

		 if (str.indexOf(at,(lat+1))!=3D-1){
		    alert("invalid email")
		    return false
		 }

		 if (str.substring(lat-1,lat)=3D=3Ddot || =
str.substring(lat+1,lat+2)=3D=3Ddot){
		    alert("invalid email")
		    return false
		 }

		 if (str.indexOf(dot,(lat+2))=3D=3D-1){
		    alert("invalid email")
		    return false
		 }
	=09
		 if (str.indexOf(" ")!=3D-1){
		    alert("invalid email")
		    return false
		 }

 		 return true				=09
	}






/**
 * DHTML phone number validation script. Courtesy of SmartWebby.com =
(http://www.smartwebby.com/dhtml/)
 */

// Declaring required variables
var digits =3D "0123456789";
// non-digit characters which are allowed in phone numbers
var phoneNumberDelimiters =3D "()- ";
// characters which are allowed in international phone numbers
// (a leading + is OK)
var validWorldPhoneChars =3D phoneNumberDelimiters + "+";
// Minimum no of digits in an international phone no.
var minDigitsInIPhoneNumber =3D 10;

function isInteger(s)
{   var i;
    for (i =3D 0; i < s.length; i++)
    {  =20
        // Check that current character is number.
        var c =3D s.charAt(i);
        if (((c < "0") || (c > "9"))) return false;
    }
    // All characters are numbers.
    return true;
}

function stripCharsInBag(s, bag)
{   var i;
    var returnString =3D "";
    // Search through string's characters one by one.
    // If character is not in bag, append to returnString.
    for (i =3D 0; i < s.length; i++)
    {  =20
        // Check that current character isn't whitespace.
        var c =3D s.charAt(i);
        if (bag.indexOf(c) =3D=3D -1) returnString +=3D c;
    }
    return returnString;
}

function checkInternationalPhone(strPhone){
s=3DstripCharsInBag(strPhone,validWorldPhoneChars);
return (isInteger(s) && s.length >=3D minDigitsInIPhoneNumber);
}



<!--
/*  TRB questionnaire form support=20
facts-research/research-technology/conference/topic-questionnaire.htm */
function doAnalysis(qnum)
{

for (i=3D0;i<document.m0101.Analysis.length;i++) {
	if (document.m0101.Analysis[i].checked) {
		var user_input =3D document.m0101.Analysis[i].id;
		if (qnum =3D=3D "q100"){
			document.m0101.analysisOther.readOnly=3Dfalse;
		}
		else
		{
			document.m0101.analysisOther.readOnly=3Dtrue;
		}
	=09
	=09

	}
}


}
function doResearch(rnum)
{

for (i=3D0;i<document.m0101.Research.length;i++) {
	if (document.m0101.Research[i].checked) {
		var user_input =3D document.m0101.Research[i].id;
		if (rnum =3D=3D "q102"){
			document.m0101.researchOther.readOnly=3Dfalse;
		}
		else
		{
			document.m0101.researchOther.readOnly=3Dtrue;
		}
	=09
	=09

	}
}


}
function doTechnology(snum)
{


for (i=3D0;i<document.m0101.Technology.length;i++) {
	if (document.m0101.Technology[i].checked) {
		var user_input =3D document.m0101.Technology[i].id;
		if (snum =3D=3D "q114"){
			document.m0101.technologyOther.readOnly=3Dfalse;
		}
		else
		{
			document.m0101.technologyOther.readOnly=3Dtrue;
		}
	=09
	=09

	}
}


}
/* end of TRB questionnaire form support */



function checkForm()
{



var isChecked =3D 0;
var validTopic =3D 0;
var  topicLength =3D 4;

var errorMsg0 =3D "Please Select at least 1 topic. \n Thank you."
var errorMsg1 =3D "Suggested topics must contain at least " + =
topicLength + " letters. "

var AnalysisTxt,auser_input;
AnalysisTxt =3D document.m0101.analysisOther.value;


   for (i=3D0; i < (document.m0101.Analysis.length - 1); i++)            =
 // is Analysis radio 1 - 4 selected
   {   =20
    var auser_input =3D document.m0101.Analysis[i].checked;

      if(auser_input =3D=3D  true)
	  {
	    isChecked =3D 1;	    =20
	   }
	 }
	=20
	      if(document.m0101.Analysis[4].checked =3D=3D true)
	 {=20
	   =20
              isChecked =3D 1;

              if(AnalysisTxt !=3D "" && AnalysisTxt !=3D null && 	 =
AnalysisTxt.length >=3D topicLength)
	      {	 	       =20
                 validTopic =3D 0;
	      }
	        else=20
	      {
	          validTopic =3D 1;
	       }
	        =20
	 }
	 	=20
	=20
/* end analysis radio check  Begin Research radio check */
 var ResearchTxt,ruser_input;
     ResearchTxt =3D document.m0101.researchOther.value;


   for (i=3D0; i < (document.m0101.Research.length - 1); i++)            =
 // is Research radio 1 - 2 selected
   {   =20
    var ruser_input =3D document.m0101.Research[i].checked;

      if(ruser_input =3D=3D  true)
	  {
	    isChecked =3D 1;	    =20
	   }
	 }
=09
	 if(document.m0101.Research[2].checked =3D=3D true)

	 {=20
            isChecked =3D 1	;
	    if(ResearchTxt !=3D "" && ResearchTxt !=3D null && 	 =
ResearchTxt.length >=3D topicLength)
	    {	 	       =20
                 validTopic =3D 0;
	      }
	        else=20
	      {
	          validTopic =3D 1;
	       }
	        =20
	 }
=09
/* end Research radio check  Begin Technology radio check */
	 	=20
var TechnologyTxt,tuser_input;
TechnologyTxt =3D document.m0101.technologyOther.value;

   for (i=3D0; i < (document.m0101.Technology.length - 1); i++)          =
   // is Technology radio 1 - 2 selected
   {   =20
    var tuser_input =3D document.m0101.Technology[i].checked;

      if(tuser_input =3D=3D  true)
	  {
	    isChecked =3D 1;	    =20
	   }
	 }
	=20
	 if(document.m0101.Technology[4].checked =3D=3D true)
	 {=20
              isChecked =3D 1	;
	    if(TechnologyTxt !=3D "" && TechnologyTxt !=3D null && 	 =
TechnologyTxt.length >=3D topicLength)
	    {	 	       =20
                 validTopic =3D 0;
	      }
	        else=20
	      {
	          validTopic =3D 1;
	       }
	        =20
	 }
	  /* end Technology radio check */


    /* process submit  */

if( isChecked =3D=3D 0 && validTopic =3D=3D 0 )
{
alert("Please select at least one topic.");
return false;
}
else if ( isChecked =3D=3D 1 && validTopic =3D=3D 1)
{
alert("Please enter a topic in the 'Other' field.");
return false;
}
else=20
{
//alert("OK to submit");
return true;
}


/*
switch (isChecked)
{
case 1:
      alert("submit the form");
      return false;
  break;   =20
case 2:
	alert(errorMsg1);
      //return an error message; =20
     return false;
  break;
default:
	alert(errorMsg0);
      //return an error message; =20
     return false;
}


*/

  /*  if(isChecked =3D=3D 1)
    {
      alert("submit the form");
      return false;
    }
	 else
    { =20
	alert(errorMsg+isChecked);
      //return an error message; =20
     return false;
    }
  */
} =20
=09
 	 function trim(string)=20
	 {
		var temp =3D "";
		string =3D '' + string;
		splitstring =3D string.split(" ");
		for(i =3D 0; i < splitstring.length; i++)
		temp +=3D splitstring[i];
		return temp;
	=09
		}  	=09

//-->



------=_NextPart_000_0000_01CA4DB9.92AFE550
Content-Type: application/octet-stream
Content-Transfer-Encoding: quoted-printable
Content-Location: http://www.fmcsa.dot.gov/foresee/foresee-trigger.js

var ForeSee =3D {
    'version': '2.5.2',
    'Date:': '1/21/2008',
    'enabled': true,
    'files': 'http://www.fmcsa.dot.gov/foresee/',
    'id': '489I0Zx5l8dlZUggUNFFMQ=3D=3D',
    'sites': [{
        path: 'fmcsa.dot.gov',
        cookie: 'session',
        domain: 'fmcsa.dot.gov'
    }]
};
/************* DO NOT ALTER ANYTHING BELOW THIS LINE ! **************/
function fsr$setAlive(){var A=3Dnew =
Date().getTime();document.cookie=3D"foresee.alive=3D"+A+";path=3D/;domain=
=3D"+ForeSee.site.domain+";"
}(function(){var C=3DForeSee.sites;for(var =
B=3D0,A=3DC.length;B<A;B++){if(document.location.href.match(C[B].path)){F=
oreSee.siteid=3DB;
ForeSee.site=3DForeSee.sites[ForeSee.siteid];if(ForeSee.site.files){ForeS=
ee.files=3DForeSee.site.files
}break}}if(!window["fsr$timer"]){fsr$setAlive();window["fsr$timer"]=3Dset=
Interval(fsr$setAlive,1000)
}})();fsr$dbug=3D{log:function(){}};ForeSee.Native=3Dfunction(J){J=3DJ||{=
};var F=3DJ.afterImplement||function(){};
var G=3DJ.generics;G=3D(G!=3D=3Dfalse);var H=3DJ.legacy;var =
E=3DJ.initialize;var B=3DJ.protect;var A=3DJ.name;var C=3DE||H;
C.xconstructor=3DForeSee.Native;C.fsr$family=3D{name:"native"};if(H&&E){C=
.prototype=3DH.prototype}C.prototype.xconstructor=3DC;
if(A){var =
D=3DA.toLowerCase();C.prototype.fsr$family=3D{name:D};ForeSee.Native.typi=
ze(C,D)}var =
I=3Dfunction(M,K,N,L){if(!B||L||!M.prototype[K]){M.prototype[K]=3DN
}if(G){ForeSee.Native.genericize(M,K,B)}F.call(M,K,N);return =
M};C.fsr$implement=3Dfunction(L,K,N){if(typeof L=3D=3D"string"){return =
I(this,L,K,N)
}for(var M in L){I(this,M,L[M],K)}return =
this};C.fsr$alias=3Dfunction(M,K,N){if(typeof =
M=3D=3D"string"){M=3Dthis.prototype[M];
if(M){I(this,K,M,N)}}else{for(var L in =
M){this.fsr$alias(L,M[L],K)}}return this};return =
C};ForeSee.Native.fsr$implement=3Dfunction(D,C){for(var =
B=3D0,A=3DD.length;
B<A;B++){D[B].fsr$implement(C)}};ForeSee.Native.genericize=3Dfunction(B,C=
,A){if((!A||!B[C])&&typeof =
B.prototype[C]=3D=3D"function"){B[C]=3Dfunction(){var =
D=3DArray.prototype.slice.call(arguments);
return =
B.prototype[C].apply(D.shift(),D)}}};ForeSee.Native.typize=3Dfunction(A,B=
){if(!A.fsr$type){A.fsr$type=3Dfunction(C){return($type(C)=3D=3D=3DB)
}}};ForeSee.Native.fsr$alias=3Dfunction(E,B,A,F){for(var =
D=3D0,C=3DE.length;D<C;D++){E[D].fsr$alias(B,A,F)
}};(function(B){for(var A in =
B){ForeSee.Native.typize(B[A],A)}})({"boolean":Boolean,"native":ForeSee.N=
ative,object:Object});
(function(B){for(var A in B){new =
ForeSee.Native({name:A,initialize:B[A],protect:true,generics:true})
}})({String:String,Function:Function,Number:Number,Array:Array,RegExp:Reg=
Exp,Date:Date});ForeSee.$chk=3Dfunction(A){return !!(A||A=3D=3D=3D0)
};ForeSee.$clear=3Dfunction(A){clearTimeout(A);clearInterval(A);return =
null};ForeSee.$defined=3Dfunction(A){return(A!=3Dundefined)
};ForeSee.$empty=3Dfunction(){};ForeSee.$arguments=3Dfunction(A){return =
function(){return arguments[A]
}};ForeSee.$lambda=3Dfunction(A){return(typeof =
A=3D=3D"function")?A:function(){return =
A}};ForeSee.$extend=3Dfunction(C,A){for(var B in (A||{})){C[B]=3DA[B]
}return C};ForeSee.$unlink=3Dfunction(C){var =
B;switch(ForeSee.$type(C)){case"object":B=3D{};for(var E in =
C){B[E]=3DForeSee.$unlink(C[E])
}break;case"hash":B=3DForeSee.$unlink(C.fsr$getClean());break;case"array"=
:B=3D[];for(var D=3D0,A=3DC.length;
D<A;D++){B[D]=3DForeSee.$unlink(C[D])}break;default:return C}return =
B};ForeSee.$merge=3Dfunction(){var E=3D{};
for(var D=3D0,A=3Darguments.length;D<A;D++){var =
B=3Darguments[D];if(ForeSee.$type(B)!=3D"object"){continue
}for(var C in B){var =
G=3DB[C],F=3DE[C];E[C]=3D(F&&ForeSee.$type(G)=3D=3D"object"&&ForeSee.$typ=
e(F)=3D=3D"object")?ForeSee.$merge(F,G):ForeSee.$unlink(G)
}}return E};ForeSee.$pick=3Dfunction(){for(var =
B=3D0,A=3Darguments.length;B<A;B++){if(arguments[B]!=3Dundefined){return =
arguments[B]
}}return =
null};ForeSee.$random=3Dfunction(B,A){return(Math.random()*(A-B))+B};Fore=
See.$splat=3Dfunction(B){var A=3DForeSee.$type(B);
return(A)?((A!=3D"array"&&A!=3D"arguments")?[B]:B):[]};ForeSee.$time=3DDa=
te.now||function(){return new Date().getTime()
};ForeSee.$try=3Dfunction(){for(var =
B=3D0,A=3Darguments.length;B<A;B++){try{return =
arguments[B]()}catch(C){}}return null
};ForeSee.$type=3Dfunction(A){if(A=3D=3Dundefined){return =
false}if(A.fsr$family){return(A.fsr$family.name=3D=3D"number"&&!isFinite(=
A))?false:A.fsr$family.name
}if(A.nodeName){switch(A.nodeType){case 1:return"element";case =
3:return(/\S/).test(A.nodeValue)?"textnode":"whitespace"
}}else{if(typeof =
A.length=3D=3D"number"){if(A.callee){return"arguments"}else{if(A.item){re=
turn"collection"
}}}}return typeof A};ForeSee.Hash=3Dnew =
ForeSee.Native({name:"Hash",initialize:function(A){if(ForeSee.$type(A)=3D=
=3D"hash"){A=3DForeSee.$unlink(A.fsr$getClean())
}for(var B in A){this[B]=3DA[B]}return =
this}});ForeSee.Hash.fsr$implement({fsr$getLength:function(){var B=3D0;
for(var A in this){if(this.hasOwnProperty(A)){B++}}return =
B},fsr$forEach:function(B,C){for(var A in =
this){if(this.hasOwnProperty(A)){B.call(C,this[A],A,this)
}}},fsr$getClean:function(){var B=3D{};for(var A in =
this){if(this.hasOwnProperty(A)){B[A]=3Dthis[A]}}return B
},fsr$empty:function(){ForeSee.Hash.fsr$each(this,function(B,A){delete =
this[A]},this);return this
}});ForeSee.Hash.fsr$alias("fsr$forEach","fsr$each");ForeSee.$H=3Dfunctio=
n(A){return new ForeSee.Hash(A)
};Array.fsr$implement({fsr$forEach:function(C,D){for(var =
B=3D0,A=3Dthis.length;B<A;B++){C.call(D,this[B],B,this)
}}});Array.fsr$alias("fsr$forEach","fsr$each");ForeSee.$A=3Dfunction(C){i=
f(C.item){var D=3D[];for(var B=3D0,A=3DC.length;
B<A;B++){D[B]=3DC[B]}return D}return =
Array.prototype.slice.call(C)};ForeSee.$each=3Dfunction(C,B,D){var =
A=3DForeSee.$type(C);
((A=3D=3D"arguments"||A=3D=3D"collection"||A=3D=3D"array")?Array:ForeSee.=
Hash).fsr$each(C,B,D)};ForeSee.Browser=3Dnew =
ForeSee.Hash({Type:{name:"unknown",version:""},Engine:{name:"unknown",ver=
sion:""},Platform:{name:(navigator.platform.match(/mac|win|linux/i)||["ot=
her"])[0].toLowerCase(),os:"unknown"},Features:{xpath:!!(document.evaluat=
e),air:!!(window.runtime)},Plugins:{},searchString:function(D){for(var =
A=3D0;
A<D.length;A++){var B=3DD[A].string;var =
C=3DD[A].prop;this.versionSearchString=3DD[A].versionSearch||D[A].identit=
y;
if(B){if(B.indexOf(D[A].subString)!=3D-1){return =
D[A].identity}}else{if(C){return =
D[A].identity}}}},searchVersion:function(B){var =
A=3DB.indexOf(this.versionSearchString);
if(A=3D=3D-1){return }return =
parseFloat(B.substring(A+this.versionSearchString.length+1))},dataBrowser=
:[{string:navigator.userAgent,subString:"Chrome",identity:"Chrome"},{stri=
ng:navigator.userAgent,subString:"OmniWeb",versionSearch:"OmniWeb/",ident=
ity:"OmniWeb"},{string:navigator.vendor,subString:"Apple",identity:"Safar=
i",versionSearch:"Version"},{prop:window.opera,identity:"Opera"},{string:=
navigator.vendor,subString:"iCab",identity:"iCab"},{string:navigator.vend=
or,subString:"KDE",identity:"Konqueror"},{string:navigator.userAgent,subS=
tring:"Firefox",identity:"Firefox"},{string:navigator.vendor,subString:"C=
amino",identity:"Camino"},{string:navigator.userAgent,subString:"Netscape=
",identity:"Netscape"},{string:navigator.userAgent,subString:"MSIE",ident=
ity:"Explorer",versionSearch:"MSIE"},{string:navigator.userAgent,subStrin=
g:"Gecko",identity:"Mozilla",versionSearch:"rv"},{string:navigator.userAg=
ent,subString:"Mozilla",identity:"Netscape",versionSearch:"Mozilla"}],dat=
aOS:[{string:navigator.platform,subString:"Win",identity:"Windows"},{stri=
ng:navigator.platform,subString:"Mac",identity:"Mac"},{string:navigator.p=
latform,subString:"Linux",identity:"Linux"}]});
if(window.opera){ForeSee.Browser.Engine=3D{name:"presto",version:(documen=
t.getElementsByClassName)?950:925}
}else{if(window.ActiveXObject){ForeSee.Browser.Engine=3D{name:"trident",v=
ersion:(window.XMLHttpRequest)?5:4}
}else{if(!navigator.taintEnabled){ForeSee.Browser.Engine=3D{name:"webkit"=
,version:(ForeSee.Browser.Features.xpath)?420:419}
}else{if(document.getBoxObjectFor!=3Dnull){ForeSee.Browser.Engine=3D{name=
:"gecko",version:(document.getElementsByClassName)?19:18}
}}}}ForeSee.Browser.Engine[ForeSee.Browser.Engine.name]=3DForeSee.Browser=
.Engine[ForeSee.Browser.Engine.name+ForeSee.Browser.Engine.version]=3Dtru=
e;
if(window.orientation!=3Dundefined){ForeSee.Browser.Platform.name=3D"ipod=
"}ForeSee.Browser.Platform[ForeSee.Browser.Platform.name]=3Dtrue;
ForeSee.Browser.Request=3Dfunction(){return =
ForeSee.$try(function(){return new XMLHttpRequest()},function(){return =
new ActiveXObject("MSXML2.XMLHTTP")
})};ForeSee.Browser.Features.xhr=3D!!(ForeSee.Browser.Request());ForeSee.=
Browser.Plugins.Flash=3D(function(){var =
A=3D(ForeSee.$try(function(){return navigator.plugins["Shockwave =
Flash"].description
},function(){return new =
ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")})|=
|"0 r0").match(/\d+/g);
return{version:parseInt(A[0]||0+"."+A[1]||0),build:parseInt(A[2]||0)}})()=
;ForeSee.Browser.Type.name=3DForeSee.Browser.searchString(ForeSee.Browser=
.dataBrowser)||"unknown";
ForeSee.Browser.Type.version=3DForeSee.Browser.searchVersion(navigator.us=
erAgent)||ForeSee.Browser.searchVersion(navigator.appVersion)||"unknown";=

ForeSee.Browser.Platform.os=3DForeSee.Browser.searchString(ForeSee.Browse=
r.dataOS)||"unknown";ForeSee.$exec=3Dfunction(B){if(!B){return B
}if(window.execScript){window.execScript(B)}else{var =
A=3Ddocument.createElement("script");A.setAttribute("type","text/javascri=
pt");
A.text=3DB;document.fsr$head.appendChild(A);document.fsr$head.removeChild=
(A)}return B};ForeSee.Native.UID=3D1;
ForeSee.$uid=3D(ForeSee.Browser.Engine.trident)?function(A){return(A.fsr$=
uid||(A.fsr$uid=3D[ForeSee.Native.UID++]))[0]
}:function(A){return =
A.fsr$uid||(A.fsr$uid=3DForeSee.Native.UID++)};ForeSee.Window=3Dnew =
ForeSee.Native({name:"Window",initialize:function(A){ForeSee.$uid(A);
if(!A.Element){A.Element=3DForeSee.$empty;if(ForeSee.Browser.Engine.webki=
t){A.document.createElement("iframe")
}A.Element.prototype=3D(ForeSee.Browser.Engine.webkit)?window["[[DOMEleme=
nt.prototype]]"]:{}}return ForeSee.$extend(A,ForeSee.Window.Prototype)
},afterImplement:function(B,A){window[B]=3DForeSee.Window.Prototype[B]=3D=
A;ForeSee.Window.Prototype[B]=3DA
}});ForeSee.Window.Prototype=3D{fsr$family:{name:"window"}};new =
ForeSee.Window(window);ForeSee.Document=3Dnew =
ForeSee.Native({name:"Document",initialize:function(A){ForeSee.$uid(A);
A.fsr$head=3DA.getElementsByTagName("head")[0];A.fsr$html=3DA.getElements=
ByTagName("html")[0];A.fsr$window=3DA.defaultView||A.parentWindow;
if(ForeSee.Browser.Engine.trident4){ForeSee.$try(function(){A.execCommand=
("BackgroundImageCache",false,true)
})}return =
ForeSee.$extend(A,ForeSee.Document.Prototype)},afterImplement:function(B,=
A){document[B]=3DForeSee.Document.Prototype[B]=3DA;
ForeSee.Document.Prototype[B]=3DA}});ForeSee.Document.Prototype=3D{fsr$fa=
mily:{name:"document"}};new ForeSee.Document(document);
Array.fsr$implement({fsr$indexOf:function(C,D){var =
A=3Dthis.length;for(var B=3D(D<0)?Math.max(0,A+D):D||0;
B<A;B++){if(this[B]=3D=3D=3DC){return B}}return =
-1},fsr$map:function(D,E){var C=3D[];for(var B=3D0,A=3Dthis.length;
B<A;B++){C[B]=3DD.call(E,this[B],B,this)}return =
C},fsr$associate:function(C){var =
D=3D{},B=3DMath.min(this.length,C.length);
for(var A=3D0;A<B;A++){D[C[A]]=3Dthis[A]}return =
D},fsr$contains:function(A,B){return this.fsr$indexOf(A,B)!=3D-1
},fsr$extend:function(C){for(var =
B=3D0,A=3DC.length;B<A;B++){this.push(C[B])}return =
this},fsr$include:function(A){if(!this.fsr$contains(A)){this.push(A)
}return this},fsr$flatten:function(){var D=3D[];for(var =
B=3D0,A=3Dthis.length;B<A;B++){var C=3DForeSee.$type(this[B]);
if(!C){continue}D=3DD.concat((C=3D=3D"array"||C=3D=3D"collection"||C=3D=3D=
"arguments")?Array.fsr$flatten(this[B]):this[B])
}return D},fsr$slice:function(){return =
Array.prototype.slice.apply(this,arguments)}});Function.fsr$implement({fs=
r$extend:function(A){for(var B in A){this[B]=3DA[B]
}return this},fsr$create:function(B){var A=3Dthis;B=3DB||{};return =
function(D){var =
C=3DB.arguments;C=3D(C!=3Dundefined)?ForeSee.$splat(C):Array.fsr$slice(ar=
guments,(B.event)?1:0);
if(B.event){C=3D[D||window.event].fsr$extend(C)}var =
E=3Dfunction(){return A.apply(B.bind||null,C)};if(B.delay){return =
setTimeout(E,B.delay)
}if(B.periodical){return =
setInterval(E,B.periodical)}if(B.attempt){return ForeSee.$try(E)}return =
E()
}},fsr$pass:function(A,B){return =
this.fsr$create({arguments:A,bind:B})},fsr$attempt:function(A,B){return =
this.fsr$create({arguments:A,bind:B,attempt:true})()
},fsr$bind:function(B,A){return =
this.fsr$create({bind:B,arguments:A})},fsr$bindWithEvent:function(B,A){re=
turn this.fsr$create({bind:B,event:true,arguments:A})
},fsr$delay:function(B,C,A){return =
this.fsr$create({delay:B,bind:C,arguments:A})()},fsr$periodical:function(=
A,C,B){return this.fsr$create({periodical:A,bind:C,arguments:B})()
},fsr$run:function(A,B){return =
this.apply(B,ForeSee.$splat(A))}});Number.fsr$implement({fsr$toInt:functi=
on(A){return parseInt(this,A||10)
}});String.fsr$implement({fsr$test:function(A,B){return((typeof =
A=3D=3D"string")?new RegExp(A,B):A).test(this)
},fsr$contains:function(A,B){return(B)?(B+this+B).indexOf(B+A+B)>-1:this.=
indexOf(A)>-1},fsr$trim:function(){return this.replace(/^\s+|\s+$/g,"")
},fsr$clean:function(){return this.replace(/\s+/g," =
").fsr$trim()},fsr$camelCase:function(){return =
this.replace(/-\D/g,function(A){return A.charAt(1).toUpperCase()
})},fsr$hyphenate:function(){return =
this.replace(/[A-Z]/g,function(A){return("-"+A.charAt(0).toLowerCase())
})},fsr$capitalize:function(){return =
this.replace(/\b[a-z]/g,function(A){return A.toUpperCase()})
},fsr$escapeRegExp:function(){return =
this.replace(/([-.*+?^${}()|[\]\/\\])/g,"\\$1")},fsr$toInt:function(A){re=
turn parseInt(this,A||10)
},fsr$stripScripts:function(B){var A=3D"";var =
C=3Dthis.replace(/<script[^>]*>([\s\S]*?)<\/script>/gi,function(){A+=3Dar=
guments[1]+"\n";
return""});if(B=3D=3D=3Dtrue){ForeSee.$exec(A)}else{if(ForeSee.$type(B)=3D=
=3D"function"){B(A,C)}}return C},fsr$substitute:function(A,B){return =
this.replace(B||(/\\?\{([^}]+)\}/g),function(D,C){if(D.charAt(0)=3D=3D"\\=
"){return D.slice(1)
}return(A[C]!=3Dundefined)?A[C]:""})}});ForeSee.Hash.fsr$implement({fsr$h=
as:Object.prototype.hasOwnProperty,fsr$keyOf:function(B){for(var A in =
this){if(this.hasOwnProperty(A)&&this[A]=3D=3D=3DB){return A
}}return =
null},fsr$extend:function(A){ForeSee.Hash.fsr$each(A,function(C,B){ForeSe=
e.Hash.fsr$set(this,B,C)
},this);return =
this},fsr$combine:function(A){ForeSee.Hash.fsr$each(A,function(C,B){ForeS=
ee.Hash.fsr$include(this,B,C)
},this);return =
this},fsr$erase:function(A){if(this.hasOwnProperty(A)){delete =
this[A]}return =
this},fsr$get:function(A){return(this.hasOwnProperty(A))?this[A]:null
},fsr$set:function(A,B){if(!this[A]||this.hasOwnProperty(A)){this[A]=3DB}=
return this},fsr$include:function(B,C){var A=3Dthis[B];
if(A=3D=3Dundefined){this[B]=3DC}return =
this},fsr$toQueryString:function(A){var =
B=3D[];ForeSee.Hash.fsr$each(this,function(F,E){if(A){E=3DA+"["+E+"]"
}var =
D;switch(ForeSee.$type(F)){case"object":D=3DForeSee.Hash.fsr$toQueryStrin=
g(F,E);break;case"array":var C=3D{};
F.fsr$each(function(H,G){C[G]=3DH});D=3DForeSee.Hash.fsr$toQueryString(C,=
E);break;default:D=3DE+"=3D"+encodeURIComponent(F)
}if(F!=3Dundefined){B.push(D)}});return =
B.join("&")}});ForeSee.Hash.fsr$alias({fsr$keyOf:"fsr$indexOf",fsr$hasVal=
ue:"fsr$contains"});
ForeSee.Event=3Dnew =
ForeSee.Native({name:"Event",initialize:function(A,F){F=3DF||window;var =
K=3DF.document;
A=3DA||F.event;if(A.fsr$extended){return A}this.fsr$extended=3Dtrue;var =
J=3DA.type;var G=3DA.target||A.srcElement;
while(G&&G.nodeType=3D=3D3){G=3DG.parentNode}if(J.fsr$test(/key/)){var =
B=3DA.which||A.keyCode;var M=3DForeSee.Event.Keys.fsr$keyOf(B);
if(J=3D=3D"keydown"){var =
D=3DB-111;if(D>0&&D<13){M=3D"f"+D}}M=3DM||String.fromCharCode(B).toLowerC=
ase()}else{if(J.match(/(click|mouse|menu)/i)){K=3D(!K.compatMode||K.compa=
tMode=3D=3D"CSS1Compat")?K.getElementsByTagName("html")[0]:K.body;
var =
I=3D{x:A.pageX||A.clientX+K.scrollLeft,y:A.pageY||A.clientY+K.scrollTop};=
var =
C=3D{x:(A.pageX)?A.pageX-F.pageXOffset:A.clientX,y:(A.pageY)?A.pageY-F.pa=
geYOffset:A.clientY};
if(J.match(/DOMMouseScroll|mousewheel/)){var =
H=3D(A.wheelDelta)?A.wheelDelta/120:-(A.detail||0)/3}var =
E=3D(A.which=3D=3D3)||(A.button=3D=3D2);
var =
L=3Dnull;if(J.match(/over|out/)){switch(J){case"mouseover":L=3DA.relatedT=
arget||A.fromElement;break;
case"mouseout":L=3DA.relatedTarget||A.toElement}if(!(function(){while(L&&=
L.nodeType=3D=3D3){L=3DL.parentNode
}return =
true}).fsr$create({attempt:ForeSee.Browser.Engine.gecko})()){L=3Dfalse}}}=
}return =
ForeSee.$extend(this,{event:A,type:J,page:I,client:C,rightClick:E,wheel:H=
,relatedTarget:L,target:G,code:B,key:M,shift:A.shiftKey,control:A.ctrlKey=
,alt:A.altKey,meta:A.metaKey})
}});ForeSee.Event.Keys=3Dnew =
ForeSee.Hash({enter:13,up:38,down:40,left:37,right:39,esc:27,space:32,bac=
kspace:8,tab:9,"delete":46});
ForeSee.Class=3Dnew =
ForeSee.Native({name:"Class",initialize:function(B){B=3DB||{};var =
A=3Dfunction(E){for(var D in this){this[D]=3DForeSee.$unlink(this[D])
}for(var F in =
ForeSee.Class.Mutators){if(!this[F]){continue}ForeSee.Class.Mutators[F](t=
his,this[F]);
delete this[F]}this.constructor=3DA;if(E=3D=3D=3DForeSee.$empty){return =
this}var =
C=3D(this.initialize)?this.initialize.apply(this,arguments):this;
if(this.options&&this.options.initialize){this.options.initialize.call(th=
is)}return C};ForeSee.$extend(A,this);
A.constructor=3DForeSee.Class;A.prototype=3DB;return =
A}});ForeSee.Class.fsr$implement({fsr$implement:function(){ForeSee.Class.=
Mutators.Implements(this.prototype,Array.fsr$slice(arguments));
return =
this}});ForeSee.Class.Mutators=3D{Implements:function(A,B){ForeSee.$splat=
(B).fsr$each(function(C){ForeSee.$extend(A,(ForeSee.$type(C)=3D=3D"class"=
)?new C(ForeSee.$empty):C)
})},Extends:function(self,klass){var instance=3Dnew =
klass(ForeSee.$empty);delete instance.parent;delete instance.parentOf;
for(var key in instance){var =
current=3Dself[key],previous=3Dinstance[key];if(current=3D=3Dundefined){s=
elf[key]=3Dprevious;
continue}var =
ctype=3DForeSee.$type(current),ptype=3DForeSee.$type(previous);if(ctype!=3D=
ptype){continue
}switch(ctype){case"function":if(!arguments.callee.caller){self[key]=3Dev=
al("("+String(current).replace(/\bthis\.parent\(\s*(\))?/g,function(full,=
close){return"arguments.callee._parent_.call(this"+(close||", ")
})+")")}self[key]._parent_=3Dprevious;break;case"object":self[key]=3DFore=
See.$merge(previous,current)
}}self.parent=3Dfunction(){return =
arguments.callee.caller._parent_.apply(this,arguments)};self.parentOf=3Df=
unction(descendant){return =
descendant._parent_.apply(this,Array.fsr$slice(arguments,1))
}}};ForeSee.Chain=3Dnew =
ForeSee.Class({chain:function(){this.$chain=3D(this.$chain||[]).fsr$exten=
d(arguments);
return =
this},callChain:function(){return(this.$chain&&this.$chain.length)?this.$=
chain.shift().apply(this,arguments):false
},clearChain:function(){if(this.$chain){this.$chain.fsr$empty()}return =
this}});ForeSee.Events=3Dnew =
ForeSee.Class({fsr$addEvent:function(C,B,A){C=3DForeSee.Events.removeOn(C=
);
if(B!=3DForeSee.$empty){this.$events=3Dthis.$events||{};this.$events[C]=3D=
this.$events[C]||[];this.$events[C].fsr$include(B);
if(A){B.internal=3Dtrue}}return this},fsr$addEvents:function(A){for(var =
B in A){this.fsr$addEvent(B,A[B])
}return =
this},fsr$fireEvent:function(C,B,A){C=3DForeSee.Events.removeOn(C);if(!th=
is.$events||!this.$events[C]){return this
}this.$events[C].fsr$each(function(D){D.fsr$create({bind:this,delay:A,"ar=
guments":B})()},this);return this
},fsr$removeEvent:function(B,A){B=3DForeSee.Events.removeOn(B);if(!this.$=
events||!this.$events[B]){return this
}if(!A.internal){this.$events[B].fsr$erase(A)}return =
this},fsr$removeEvents:function(C){for(var D in =
this.$events){if(C&&C!=3DD){continue
}var B=3Dthis.$events[D];for(var =
A=3DB.length;A--;A){this.fsr$removeEvent(D,B[A])}}return =
this}});ForeSee.Events.removeOn=3Dfunction(A){return =
A.replace(/^on([A-Z])/,function(B,C){return C.toLowerCase()
})};ForeSee.Options=3Dnew =
ForeSee.Class({setOptions:function(){this.options=3DForeSee.$merge.fsr$ru=
n([this.options].fsr$extend(arguments));
if(!this.fsr$addEvent){return this}for(var A in =
this.options){if(ForeSee.$type(this.options[A])!=3D"function"||!(/^on[A-Z=
]/).test(A)){continue
}this.fsr$addEvent(A,this.options[A]);delete this.options[A]}return =
this}});ForeSee.Document.fsr$implement({fsr$newElement:function(A,B){if(F=
oreSee.Browser.Engine.trident&&B){["name","type","checked"].fsr$each(func=
tion(C){if(!B[C]){return=20
}A+=3D" "+C+'=3D"'+B[C]+'"';if(C!=3D"checked"){delete =
B[C]}});A=3D"<"+A+">"}return =
$fsr.element(this.createElement(A)).fsr$set(B)
},fsr$newTextNode:function(A){return =
this.createTextNode(A)},fsr$getDocument:function(){return this
},fsr$getWindow:function(){return =
this.defaultView||this.parentWindow},fsr$purge:function(){var =
C=3Dthis.getElementsByTagName("*");
for(var =
B=3D0,A=3DC.length;B<A;B++){ForeSee.Browser.freeMem(C[B])}}});ForeSee.Ele=
ment=3Dnew ForeSee.Native({name:"Element",initialize:function(A,B){var =
C=3DForeSee.Element.Constructors.fsr$get(A);
if(C){return C(B)}if(typeof A=3D=3D"string"){return =
document.fsr$newElement(A,B)}return $fsr(A).fsr$set(B)
},afterImplement:function(A,B){if(!Array[A]){ForeSee.Elements.fsr$impleme=
nt(A,ForeSee.Elements.fsr$multi(A))
}ForeSee.Element.Prototype[A]=3DB}});ForeSee.Element.Prototype=3D{fsr$fam=
ily:{name:"element"}};ForeSee.Element.Constructors=3Dnew ForeSee.Hash;
ForeSee.Elements=3Dnew =
ForeSee.Native({initialize:function(F,B){B=3DForeSee.$extend({ddup:true,c=
ash:true},B);
F=3DF||[];if(B.ddup||B.cash){var G=3D{},E=3D[];for(var =
C=3D0,A=3DF.length;C<A;C++){var D=3D$fsr.element(F[C],!B.cash);
if(B.ddup){if(G[D.fsr$uid]){continue}G[D.fsr$uid]=3Dtrue}E.push(D)}F=3DE}=
return(B.cash)?ForeSee.$extend(F,this):F
}});ForeSee.Elements.fsr$implement({fsr$filter:function(A,B){if(!A){retur=
n this}return new ForeSee.Elements(Array.fsr$filter(this,(typeof =
A=3D=3D"string")?function(C){return C.match(A)
}:A,B))}});ForeSee.Elements.fsr$multi=3Dfunction(A){return =
function(){var B=3D[];var F=3Dtrue;for(var D=3D0,C=3Dthis.length;
D<C;D++){var =
E=3Dthis[D][A].apply(this[D],arguments);B.push(E);if(F){F=3D(ForeSee.$typ=
e(E)=3D=3D"element")
}}return(F)?new =
ForeSee.Elements(B):B}};ForeSee.Window.fsr$implement({$fsr:function(B,C){=
if(B&&B.fsr$family&&B.fsr$uid){return B
}var =
A=3DForeSee.$type(B);return($fsr[A])?$fsr[A](B,C,this.document):null},$$f=
sr:function(A){if(arguments.length=3D=3D1&&typeof =
A=3D=3D"string"){return this.document.fsr$getElements(A)
}var F=3D[];var C=3DArray.fsr$flatten(arguments);for(var =
D=3D0,B=3DC.length;D<B;D++){var =
E=3DC[D];switch(ForeSee.$type(E)){case"element":E=3D[E];
break;case"string":E=3Dthis.document.fsr$getElements(E,true);break;defaul=
t:E=3Dfalse}if(E){F.fsr$extend(E)
}}return new ForeSee.Elements(F)},fsr$getDocument:function(){return =
this.document},fsr$getWindow:function(){return this
}});$fsr.string=3Dfunction(C,B,A){C=3DA.getElementById(C);return(C)?$fsr.=
element(C,B):null};$fsr.element=3Dfunction(A,D){ForeSee.$uid(A);
if(!D&&!A.fsr$family&&!(/^object|embed$/i).test(A.tagName)){var =
B=3DForeSee.Element.Prototype;for(var C in B){A[C]=3DB[C]
}}return A};$fsr.object=3Dfunction(B,C,A){if(B.toElement){return =
$fsr.element(B.toElement(A),C)}return null
};$fsr.textnode=3D$fsr.whitespace=3D$fsr.window=3D$fsr.document=3DForeSee=
.$arguments(0);ForeSee.Native.fsr$implement([ForeSee.Element,ForeSee.Docu=
ment],{fsr$getElement:function(A,B){return =
$fsr(this.fsr$getElements(A,true)[0]||null,B)
},fsr$getElements:function(A,D){A=3DA.split(",");var C=3D[];var =
B=3D(A.length>1);A.fsr$each(function(E){var =
F=3Dthis.getElementsByTagName(E.fsr$trim());
(B)?C.fsr$extend(F):C=3DF},this);return new =
ForeSee.Elements(C,{ddup:B,cash:!D})}});ForeSee.Element.Storage=3D{fsr$ge=
t:function(A){return(this[A]||(this[A]=3D{}))
}};ForeSee.Element.Inserters=3Dnew =
ForeSee.Hash({after:function(B,A){if(!A.parentNode){return }var =
C=3DA.nextSibling;
(C)?A.parentNode.insertBefore(B,C):A.parentNode.appendChild(B)},bottom:fu=
nction(B,A){A.appendChild(B)
}});ForeSee.Element.Inserters.inside=3DForeSee.Element.Inserters.bottom;F=
oreSee.Element.fsr$implement({fsr$getDocument:function(){return =
this.ownerDocument
},fsr$getWindow:function(){return =
this.ownerDocument.fsr$getWindow()},fsr$set:function(D,B){switch(ForeSee.=
$type(D)){case"object":for(var C in D){this.fsr$set(C,D[C])
}break;case"string":var =
A=3DForeSee.Element.Properties.fsr$get(D);(A&&A.fsr$set)?A.fsr$set.apply(=
this,Array.fsr$slice(arguments,1)):this.fsr$setProperty(D,B)
}return =
this},fsr$inject:function(B,A){ForeSee.Element.Inserters.fsr$get(A||"bott=
om")(this,$fsr(B,true));
return =
this},fsr$dispose:function(){return(this.parentNode)?this.parentNode.remo=
veChild(this):this
},fsr$setProperty:function(D,E){var =
C=3DForeSee.Element.Attributes,B=3DC.Props[D],A=3DForeSee.$defined(E);
if(B&&C.Bools[D]){E=3D(E||!A)?true:false}else{if(!A){return =
this.removeProperty(D)}}(B)?this[B]=3DE:this.setAttribute(D,E);
return this},fsr$setProperties:function(A){for(var B in =
A){this.fsr$setProperty(B,A[B])}return this
}});ForeSee.Element.Properties=3Dnew =
ForeSee.Hash;ForeSee.Element.Properties.html=3D{fsr$set:function(){return=
 this.innerHTML=3DArray.fsr$flatten(arguments).join("")
}};ForeSee.Native.fsr$implement([ForeSee.Element,ForeSee.Window,ForeSee.D=
ocument],{fsr$addListener:function(B,A){if(this.addEventListener){this.ad=
dEventListener(B,A,false)
}else{this.attachEvent("on"+B,A)}return =
this},fsr$removeListener:function(B,A){if(this.removeEventListener){this.=
removeEventListener(B,A,false)
}else{this.detachEvent("on"+B,A)}return =
this},fsr$retrieve:function(B,A){var =
D=3DForeSee.Element.Storage.fsr$get(this.fsr$uid);
var =
C=3DD[B];if(ForeSee.$defined(A)&&!ForeSee.$defined(C)){C=3DD[B]=3DA}retur=
n ForeSee.$pick(C)},fsr$store:function(B,A){var =
C=3DForeSee.Element.Storage.fsr$get(this.fsr$uid);
C[B]=3DA;return this},fsr$eliminate:function(A){var =
B=3DForeSee.Element.Storage.fsr$get(this.fsr$uid);
delete B[A];return this}});ForeSee.Element.Attributes=3Dnew =
ForeSee.Hash({Props:{html:"innerHTML","class":"className","for":"htmlFor"=
,text:(ForeSee.Browser.Engine.trident)?"innerText":"textContent"},Bools:[=
"compact","nowrap","ismap","declare","noshade","checked","disabled","read=
only","multiple","selected","noresize","defer"],Camels:["value","accessKe=
y","cellPadding","cellSpacing","colSpan","frameBorder","maxLength","readO=
nly","rowSpan","tabIndex","useMap"]});
ForeSee.Browser.freeMem=3Dfunction(A){if(!A){return =
}if(ForeSee.Browser.Engine.trident&&(/object/i).test(A.tagName)){for(var =
B in A){if(typeof A[B]=3D=3D"function"){A[B]=3DForeSee.$empty
}}ForeSee.Element.fsr$dispose(A)}if(A.fsr$uid&&A.fsr$removeEvents){A.fsr$=
removeEvents()}};(function(A){var C=3DA.Bools,B=3DA.Camels;
A.Bools=3DC=3DC.fsr$associate(C);ForeSee.Hash.fsr$extend(ForeSee.Hash.fsr=
$combine(A.Props,C),B.fsr$associate(B.fsr$map(function(D){return =
D.toLowerCase()
})));A.fsr$erase("Camels")})(ForeSee.Element.Attributes);window.fsr$addLi=
stener("unload",function(){window.fsr$removeListener("unload",arguments.c=
allee);
document.fsr$purge();if(ForeSee.Browser.Engine.trident){CollectGarbage()}=
});ForeSee.Element.Properties.events=3D{fsr$set:function(A){this.fsr$addE=
vents(A)
}};ForeSee.Native.fsr$implement([ForeSee.Element,ForeSee.Window,ForeSee.D=
ocument],{fsr$addEvent:function(E,G){var =
H=3Dthis.fsr$retrieve("events",{});
H[E]=3DH[E]||{keys:[],values:[]};if(H[E].keys.fsr$contains(G)){return =
this}H[E].keys.push(G);var =
F=3DE,A=3DForeSee.Element.Events.fsr$get(E),C=3DG,I=3Dthis;
if(A){if(A.onAdd){A.onAdd.call(this,G)}if(A.condition){C=3Dfunction(J){if=
(A.condition.call(this,J)){return G.call(this,J)
}return false}}F=3DA.base||F}var D=3Dfunction(){return G.call(I)};var =
B=3DForeSee.Element.NativeEvents[F]||0;
if(B){if(B=3D=3D2){D=3Dfunction(J){J=3Dnew =
ForeSee.Event(J,I.fsr$getWindow());if(C.call(I,J)=3D=3D=3Dfalse){J.stop()=

}}}this.fsr$addListener(F,D)}H[E].values.push(D);return =
this},fsr$removeEvent:function(D,C){var B=3Dthis.fsr$retrieve("events");
if(!B||!B[D]){return this}var =
G=3DB[D].keys.fsr$indexOf(C);if(G=3D=3D-1){return this}var =
A=3DB[D].keys.splice(G,1)[0];
var F=3DB[D].values.splice(G,1)[0];var =
E=3DForeSee.Element.Events.fsr$get(D);if(E){if(E.onRemove){E.onRemove.cal=
l(this,C)
}D=3DE.base||D}return(ForeSee.Element.NativeEvents[D])?this.fsr$removeLis=
tener(D,F):this},fsr$addEvents:function(A){for(var B in =
A){if(A.hasOwnProperty(B)){this.fsr$addEvent(B,A[B])
}}return this},fsr$removeEvents:function(B){var =
A=3Dthis.fsr$retrieve("events");if(!A){return this}if(!B){for(var C in =
A){if(A.hasOwnProperty(C)){this.fsr$removeEvents(C)
}}A=3Dnull}else{if(A[B]){while(A[B].keys[0]){this.fsr$removeEvent(B,A[B].=
keys[0])}A[B]=3Dnull}}return this
},fsr$fireEvent:function(D,B,A){var =
C=3Dthis.fsr$retrieve("events");if(!C||!C[D]){return =
this}C[D].keys.fsr$each(function(E){E.fsr$create({bind:this,delay:A,"argu=
ments":B})()
},this);return =
this}});ForeSee.Element.NativeEvents=3D{click:2,dblclick:2,mouseup:2,mous=
edown:2,contextmenu:2,mousewheel:2,DOMMouseScroll:2,mouseover:2,mouseout:=
2,mousemove:2,selectstart:2,selectend:2,keydown:2,keypress:2,keyup:2,focu=
s:2,blur:2,change:2,reset:2,select:2,submit:2,load:1,unload:1,beforeunloa=
d:2,resize:1,move:1,DOMContentLoaded:1,readystatechange:1,error:1,abort:1=
,scroll:1};
(function(){ForeSee.$check=3Dfunction(A){var =
B=3DA.relatedTarget;if(B=3D=3Dundefined){return =
true}if(B=3D=3D=3Dfalse){return false
}return(ForeSee.$type(this)!=3D"document"&&B!=3Dthis&&B.prefix!=3D"xul"&&=
!this.fsr$hasChild(B))};ForeSee.Element.Events=3Dnew =
ForeSee.Hash({mouseenter:{base:"mouseover",condition:ForeSee.$check},mous=
eleave:{base:"mouseout",condition:ForeSee.$check},mousewheel:{base:(ForeS=
ee.Browser.Engine.gecko)?"DOMMouseScroll":"mousewheel"}})
})();ForeSee.Element.fsr$implement({fsr$hasChild:function(A){A=3D$fsr(A,t=
rue);return(!!A&&ForeSee.$A(this.getElementsByTagName(A.tagName)).fsr$con=
tains(A))
}});(function(){ForeSee.Native.fsr$implement([ForeSee.Document,ForeSee.Wi=
ndow],{fsr$getSize:function(){var C=3Dthis.fsr$getWindow();
if(ForeSee.Browser.Engine.presto||ForeSee.Browser.Engine.webkit){return{x=
:C.innerWidth,y:C.innerHeight}
}var =
B=3DA(this);return{x:B.clientWidth,y:B.clientHeight}},fsr$getScroll:funct=
ion(){var C=3Dthis.fsr$getWindow();
var =
B=3DA(this);return{x:C.pageXOffset||B.scrollLeft,y:C.pageYOffset||B.scrol=
lTop}},fsr$getScrollSize:function(){var C=3DA(this);
var =
B=3Dthis.fsr$getSize();return{x:Math.max(C.scrollWidth,B.x),y:Math.max(C.=
scrollHeight,B.y)}}});
function A(B){var =
C=3DB.fsr$getDocument();return(!C.compatMode||C.compatMode=3D=3D"CSS1Comp=
at")?C.getElementsByTagName("html")[0]:C.body
}})();ForeSee.Element.Events.domready=3D{onAdd:function(A){if(ForeSee.Bro=
wser.loaded){A.call(this)}}};
(function(){var A=3Dfunction(){if(ForeSee.Browser.loaded){return =
}ForeSee.Browser.loaded=3Dtrue;window.fsr$fireEvent("domready");
document.fsr$fireEvent("domready")};switch(ForeSee.Browser.Engine.name){c=
ase"webkit":(function(){(["loaded","complete"].fsr$contains(document.read=
yState))?A():arguments.callee.fsr$delay(50)
})();break;case"trident":var =
B=3Ddocument.createElement("div");(function(){(ForeSee.$try(function(){B.=
doScroll("left");
return =
$fsr(B).fsr$inject(document.body).fsr$set("html","temp").fsr$dispose()}))=
?A():arguments.callee.fsr$delay(50)
})();break;default:window.fsr$addEvent("load",A);document.fsr$addEvent("D=
OMContentLoaded",A)}})();
ForeSee.JSON=3Dnew =
ForeSee.Hash({encode:function(B){switch(ForeSee.$type(B)){case"string":re=
turn'"'+B.replace(/[\x00-\x1f\\"]/g,ForeSee.JSON.$replaceChars)+'"';
case"array":return"["+String(B.fsr$map(ForeSee.JSON.encode).fsr$filter(Fo=
reSee.$defined))+"]";case"object":case"hash":var A=3D[];
ForeSee.Hash.fsr$each(B,function(E,D){var =
C=3DForeSee.JSON.encode(E);if(C){A.push(ForeSee.JSON.encode(D)+":"+C)
}});return"{"+A+"}";case"number":case"boolean":return String(B);case =
false:return"null"}return null
},$specialChars:{"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'=
"':'\\"',"\\":"\\\\"},$replaceChars:function(A){return =
ForeSee.JSON.$specialChars[A]||"\\u00"+Math.floor(A.charCodeAt()/16).toSt=
ring(16)+(A.charCodeAt()%16).toString(16)
},decode:function(string,secure){if(ForeSee.$type(string)!=3D"string"||!s=
tring.length){return null}if(secure&&!(/^[,:{}\[\]0-9.\-+Eaeflnr-u =
\n\r\t]*$/).test(string.replace(/\\./g,"@").replace(/"[^"\\\n\r]*"/g,""))=
){return null
}return =
eval("("+string+")")}});ForeSee.Native.fsr$implement([ForeSee.Hash,Array,=
String,Number],{fsr$toJSON:function(){return ForeSee.JSON.encode(this)
}});ForeSee.Cookie=3Dnew =
ForeSee.Class({Implements:ForeSee.Options,options:{path:false,domain:fals=
e,duration:false,secure:false,document:document},initialize:function(B,A)=
{this.key=3DB;
this.setOptions(A)},write:function(B){B=3DencodeURIComponent(B);if(this.o=
ptions.domain){B+=3D"; domain=3D"+this.options.domain
}if(this.options.path){B+=3D"; =
path=3D"+this.options.path}if(this.options.duration){var A=3Dnew Date();
A.setTime(A.getTime()+this.options.duration*24*60*60*1000);B+=3D"; =
expires=3D"+A.toGMTString()}if(this.options.secure){B+=3D"; secure"
}this.options.document.cookie=3Dthis.key+"=3D"+B;return =
this},read:function(){var =
A=3Dthis.options.document.cookie.match("(?:^|;)\\s*"+this.key.fsr$escapeR=
egExp()+"=3D([^;]*)");
return(A)?decodeURIComponent(A[1]):null},dispose:function(){new =
ForeSee.Cookie(this.key,ForeSee.$merge(this.options,{duration:-1})).write=
("");
return this}});ForeSee.Cookie.write=3Dfunction(B,C,A){return new =
ForeSee.Cookie(B,A).write(C)};ForeSee.Cookie.read=3Dfunction(A){return =
new ForeSee.Cookie(A).read()
};ForeSee.Cookie.dispose=3Dfunction(B,A){return new =
ForeSee.Cookie(B,A).dispose()};ForeSee.Hash.Cookie=3Dnew =
ForeSee.Class({Extends:ForeSee.Cookie,options:{autoSave:true},initialize:=
function(B,A){this.parent(B,A);
this.load()},save:function(){var =
A=3DForeSee.JSON.encode(this.hash);if(!A||A.length>4096){return false
}if(A=3D=3D"{}"){this.dispose()}else{this.write(A)}return =
true},load:function(){this.hash=3Dnew =
ForeSee.Hash(ForeSee.JSON.decode(this.read(),true));
return =
this}});ForeSee.Hash.Cookie.fsr$implement({fsr$get:function(A){return =
this.hash.fsr$get(A)
},fsr$set:function(A,B){this.hash.fsr$set(A,B);this.save();return =
this},fsr$erase:function(A){this.hash.fsr$erase(A);
this.save();return =
this},fsr$empty:function(){this.hash.fsr$empty();this.save();return =
this}});ForeSee.Asset=3Dnew =
ForeSee.Hash({javascript:function(F,D){D=3DForeSee.$extend({onload:ForeSe=
e.$empty,document:document,check:ForeSee.$lambda(true)},D);
var B=3Dnew ForeSee.Element("script",{src:F,type:"text/javascript"});var =
E=3DD.onload.fsr$bind(B),A=3DD.check,G=3DD.document;
delete D.onload;delete D.check;delete =
D.document;B.fsr$addEvents({load:E,readystatechange:function(){if(ForeSee=
.Browser.Engine.trident&&["loaded","complete"].fsr$contains(this.readySta=
te)){E()
}}}).fsr$setProperties(D);if(ForeSee.Browser.Engine.webkit419){var =
C=3D(function(){if(!ForeSee.$try(A)){return=20
}ForeSee.$clear(C);E()}).fsr$periodical(50)}return =
B.fsr$inject(document.getElementsByTagName("head")[0])
},image:function(C,B){B=3DForeSee.$merge({onload:ForeSee.$empty,onabort:F=
oreSee.$empty,onerror:ForeSee.$empty},B);
var D=3Dnew Image();var A=3D$fsr(D)||new =
ForeSee.Element("img");["load","abort","error"].fsr$each(function(E){var =
F=3D"on"+E;
var G=3DB[F];delete B[F];D[F]=3Dfunction(){if(!D){return =
}if(!A.parentNode){A.width=3DD.width;A.height=3DD.height
}D=3DD.onload=3DD.onabort=3DD.onerror=3Dnull;G.fsr$delay(1,A,A);A.fsr$fir=
eEvent(E,A,1)}});D.src=3DC;if(A.src!=3DD.src){A.src=3DD.src
}if(D&&D.complete){D.onload.fsr$delay(1)}return =
A.fsr$setProperties(B)},css:function(B,A){return new =
ForeSee.Element("link",ForeSee.$merge({rel:"stylesheet",media:"screen",ty=
pe:"text/css",href:B},A)).fsr$inject(document.getElementsByTagName("head"=
)[0])
}});ForeSee.Request=3Dnew =
ForeSee.Class({Implements:[ForeSee.Chain,ForeSee.Events,ForeSee.Options],=
options:{url:"",data:"",headers:{"X-ForeSee.Requested-With":"XMLHttpForeS=
ee.Request",Accept:"text/javascript, text/html, application/xml, =
text/xml, =
*/*"},async:true,format:false,method:"post",link:"ignore",isSuccess:null,=
emulation:true,urlEncoded:true,encoding:"utf-8",evalScripts:false,evalRes=
ponse:false},initialize:function(A){this.xhr=3Dnew =
ForeSee.Browser.Request();
this.setOptions(A);this.options.isSuccess=3Dthis.options.isSuccess||this.=
isSuccess;this.headers=3Dnew ForeSee.Hash(this.options.headers)
},onStateChange:function(){if(this.xhr.readyState!=3D4||!this.running){re=
turn }this.running=3Dfalse;this.status=3D0;
ForeSee.$try(function(){this.status=3Dthis.xhr.status}.fsr$bind(this));if=
(this.options.isSuccess.call(this,this.status)){this.response=3D{text:thi=
s.xhr.responseText,xml:this.xhr.responseXML};
this.success(this.response.text,this.response.xml)}else{this.response=3D{=
text:null,xml:null};this.failure()
}this.xhr.onreadystatechange=3DForeSee.$empty},isSuccess:function(){retur=
n((this.status>=3D200)&&(this.status<300))
},processScripts:function(A){if(this.options.evalResponse||(/(ecma|java)s=
cript/).test(this.getHeader("Content-type"))){return ForeSee.$exec(A)
}return =
A.fsr$stripScripts(this.options.evalScripts)},success:function(B,A){this.=
onSuccess(this.processScripts(B),A)
},onSuccess:function(){this.fsr$fireEvent("complete",arguments).fsr$fireE=
vent("success",arguments).callChain()
},failure:function(){this.onFailure()},onFailure:function(){this.fsr$fire=
Event("complete").fsr$fireEvent("failure",this.xhr)
},setHeader:function(A,B){this.headers.fsr$set(A,B);return =
this},getHeader:function(A){return ForeSee.$try(function(){return =
this.xhr.getResponseHeader(A)
}.fsr$bind(this))},check:function(A){if(!this.running){return =
true}switch(this.options.link){case"cancel":this.cancel();
return =
true;case"chain":this.chain(A.fsr$bind(this,Array.fsr$slice(arguments,1))=
);return false}return false
},send:function(C){if(!this.check(arguments.callee,C)){return =
this}this.running=3Dtrue;var D=3DForeSee.$type(C);
if(D=3D=3D"string"||D=3D=3D"element"){C=3D{data:C}}var =
A=3Dthis.options;C=3DForeSee.$extend({data:A.data,url:A.url,method:A.meth=
od},C);
var =
E=3DC.data,B=3DC.url,F=3DC.method;switch(ForeSee.$type(E)){case"element":=
E=3D$fsr(E).fsr$toQueryString();
break;case"object":case"hash":E=3DForeSee.Hash.fsr$toQueryString(E)}if(E&=
&F=3D=3D"get"){B=3DB+(B.fsr$contains("?")?"&":"?")+E;
E=3Dnull}this.xhr.open(F.toUpperCase(),B,this.options.async);this.xhr.onr=
eadystatechange=3Dthis.onStateChange.fsr$bind(this);
this.headers.fsr$each(function(H,G){if(!ForeSee.$try(function(){this.xhr.=
setRequestHeader(G,H);return true
}.fsr$bind(this))){this.fsr$fireEvent("exception",[G,H])}},this);this.fsr=
$fireEvent("request");this.xhr.send(E);
if(!this.options.async){this.onStateChange()}return =
this},cancel:function(){if(!this.running){return this
}this.running=3Dfalse;this.xhr.abort();this.xhr.onreadystatechange=3DFore=
See.$empty;this.xhr=3Dnew ForeSee.Browser.Request();
this.fsr$fireEvent("cancel");return this}});(function(){var =
A=3D{};["get","post","put","delete","GET","POST","PUT","DELETE"].fsr$each=
(function(B){A[B]=3Dfunction(){var =
C=3DArray.fsr$link(arguments,{url:String.type,data:ForeSee.$defined});
return =
this.send(ForeSee.$extend(C,{method:B.toLowerCase()}))}});ForeSee.Request=
.fsr$implement(A)
})();ForeSee.Browser.fsr$set("Popup",new =
ForeSee.Class({Implements:[ForeSee.Options,ForeSee.Events],options:{width=
:500,height:300,x:50,y:50,toolbar:0,location:0,directories:0,status:0,scr=
ollbars:"auto",resizable:1,name:"popup",blur:false,menubar:1},initialize:=
function(B,A){this.url=3DB||false;
this.setOptions(A);if(this.url){this.openWin()}},openWin:function(B){B=3D=
B||this.url;var =
A=3D"toolbar=3D"+this.options.toolbar+",location=3D"+this.options.locatio=
n+",directories=3D"+this.options.directories+",status=3D"+this.options.st=
atus+",scrollbars=3D"+this.options.scrollbars+",resizable=3D"+this.option=
s.resizable+",width=3D"+this.options.width+",height=3D"+this.options.heig=
ht+",top=3D"+this.options.y+",left=3D"+this.options.x+",menubar=3D"+this.=
options.menubar;
this.window=3Dwindow.open(B,this.options.name,A);if(!this.window){this.wi=
ndow=3Dwindow.open("",this.options.name,A);
this.window.location.href=3DB}if(!this.options.blur){this.focus.fsr$delay=
(100,this)}else{this.window.blur()
}return =
this},focus:function(){if(this.window){this.window.focus()}else{if(this.f=
ocusTries<10){this.focus.delay(100,this)
}else{this.blocked=3Dtrue;this.fsr$fireEvent("onBlock")}}return =
this},focusTries:0,blocked:null,close:function(){this.window.close();
return this}}));ForeSee.RemoteEvent=3Dnew =
ForeSee.Class({Implements:[ForeSee.Chain,ForeSee.Events,ForeSee.Options],=
options:{host:"",path:"",url:""},initialize:function(B,A){this.setOptions=
(A);
this.event=3DB},onStateChange:function(A){if(!this.running){return =
}this.running=3Dfalse;this.status=3D0;
ForeSee.$try(function(){this.status=3DA}.fsr$bind(this));if(this.isSucces=
s()){this.success()}else{this.failure()
}},isSuccess:function(){return(this.status=3D=3D1)},success:function(){th=
is.onSuccess()},onSuccess:function(){this.fsr$fireEvent("success")
},failure:function(){this.onFailure()},onFailure:function(){this.fsr$fire=
Event("failure")},send:function(B){this.running=3Dtrue;
var A=3Dthis;var D=3DForeSee.Hash.fsr$toQueryString(B);var =
C=3Ddocument.location.protocol+"//"+this.options.host+this.options.path+t=
his.options.url+"?event=3D"+this.event+"&"+D+"&uid=3D"+ForeSee.$time();
new =
ForeSee.Asset.image(C,{onload:function(E){A.onStateChange(1)},onerror:fun=
ction(){A.onStateChange(0)
},onabort:function(){A.onStateChange(0)}});return =
this}});ForeSee.RemoteCookie=3Dnew =
ForeSee.Class({Implements:ForeSee.Options,options:{host:false,path:false,=
url:false,duration:false},initialize:function(B,A){this.id=3DB;
this.setOptions(A)},save:function(A,B){if(A&&B){new =
ForeSee.RemoteEvent("setcookie",this.options).send({id:this.id,name:A,val=
ue:B})
}else{if(!B){new =
ForeSee.RemoteEvent("deletecookie",this.options).send({id:this.id,name:A}=
)}else{if(!A&&!B){new =
ForeSee.RemoteEvent("deletecookie",this.options).send({id:this.id})
}}}return true},load:function(A){return =
this}});ForeSee.RemoteCookie.fsr$implement({fsr$get:function(A){this.load=
(A)
},fsr$set:function(A,B){this.save(A,B);return =
this},fsr$erase:function(A){this.save(A);return this
},fsr$empty:function(){this.save();return this}});ForeSee.CPPS=3Dnew =
ForeSee.Hash({fsr$set:function(B,C){var =
A=3DForeSee.c().fsr$get("cpps")||{};
A[B]=3DC;ForeSee.c().fsr$set("cpps",A)},fsr$get:function(B){var =
A=3DForeSee.c().fsr$get("cpps")||{};return A[B]
},fsr$erase:function(B){var A=3DForeSee.c().fsr$get("cpps")||{};delete =
A[B];ForeSee.c().fsr$set("cpps",A)
},fsr$toQueryString:function(){var G=3DForeSee.c();var =
D=3DG.fsr$get("browser");var F=3D{browser:D.name+" =
"+D.version,os:D.platform,pv:G.fsr$get("pv"),url:G.fsr$get("current"),loc=
ale:G.fsr$get("locale")||"",site:G.fsr$get("site")||"",referrer:G.fsr$get=
("referrer")||"",terms:G.fsr$get("terms")||""};
var C=3DG.fsr$get("cpps")||{};var E=3Dnew ForeSee.Hash(C);var =
B=3DF||{};for(k in B){E.fsr$set(k,B[k])}var =
A=3DE.fsr$toQueryString("cpp");
return A}});ForeSee.Service=3Dnew =
ForeSee.Class({Implements:[ForeSee.Chain,ForeSee.Events,ForeSee.Options],=
options:{},initialize:function(A){this.setOptions(A)
},onStateChange:function(A){if(!this.running){return =
}this.running=3Dfalse;this.status=3D0;ForeSee.$try(function(){this.status=
=3DA
}.fsr$bind(this));if(this.status=3D=3D1){this.success()}else{if(this.stat=
us=3D=3D0){this.failure()}else{if(this.status=3D=3D-1){this.error()
}}}},success:function(){this.onSuccess()},onSuccess:function(){this.fsr$f=
ireEvent("complete").fsr$fireEvent("success")
},failure:function(){this.onFailure()},onFailure:function(){this.fsr$fire=
Event("complete").fsr$fireEvent("failure")
},error:function(){this.onError()},onError:function(){this.fsr$fireEvent(=
"complete").fsr$fireEvent("error")
},ping:function(){this.running=3Dtrue;var B=3Dthis;var =
D=3Dthis.options.params||{};D.protocol=3Ddocument.location.protocol;
D.uid=3DForeSee.$time();var A=3DForeSee.Hash.fsr$toQueryString(D);var =
C=3Ddocument.location.protocol+"//"+this.options.host+this.options.path+t=
his.options.url+"?"+A;
new =
ForeSee.Asset.image(C,{onload:function(E){if(E.width=3D=3DB.options.succe=
ss){B.onStateChange(1)}else{B.onStateChange(0)
}},onerror:function(){B.onStateChange(-1)},onabort:function(){B.onStateCh=
ange(0)}});return this},cancel:function(){if(!this.running){return this
}this.running=3Dfalse;this.fsr$fireEvent("cancel");return =
this}});ForeSee.services=3D{survey:{host:"www.foreseeresults.com",path:"/=
survey",url:"/display"},check:{host:"controller.foreseeresults.com",path:=
"/fsrSurvey",url:"/OTCImg",success:3},event:{host:"event.foreseeresults.c=
om",path:"/fsrsurvey",url:"/processEvent",enabled:false},domain:{host:"ww=
w.foreseeresults.com",path:"/survey",url:"/FSRImg",success:3}};
ForeSee.UnsupportedBrowsers=3D{Explorer:5.5,Safari:2,Firefox:1.4};ForeSee=
.$P=3Dfunction(){return ForeSee.properties
};ForeSee.c=3Dfunction(){return new =
ForeSee.Hash.Cookie("foresee."+ForeSee.site.cookie,{path:"/",domain:ForeS=
ee.site.domain||false})
};ForeSee.log=3Dfunction(B,A){if(!ForeSee.services.event.enabled){return =
}new =
ForeSee.RemoteEvent("logit",{host:ForeSee.services.event.host,path:ForeSe=
e.services.event.path,url:ForeSee.services.event.url}).send({id:"foresee.=
"+ForeSee.site.cookie,msg:B,param:A,cid:ForeSee.id})
};ForeSee.popNow=3Dfunction(A){ForeSee.pop(A,"now")};ForeSee.popLater=3Df=
unction(A){ForeSee.pop(A,"later")
};ForeSee.popImmediate=3Dfunction(){ForeSee.pop(100,"now")};ForeSee.popFe=
edback=3Dfunction(){ForeSee.controller.popFeedback()
};ForeSee.pop=3Dfunction(D,A){var =
C=3DD;if(!C){C=3DForeSee.controller.sd.sp}var =
B=3DA;if(!B){B=3DForeSee.controller.sd.pop.when
}var E=3DForeSee.controller.sd.sv;if(!(E<=3DC)){return =
}if(B=3D=3D"now"){if(!(ForeSee.controller.surveyShown()&&C<100)){ForeSee.=
controller.popImmediate()
}}else{if(B=3D=3D"later"){if(!ForeSee.controller.trackerRunning()){ForeSe=
e.controller.popTracker()}}}};
ForeSee.close=3Dfunction(){ForeSee.controller.cancelTracker()};ForeSee.ru=
n=3Dfunction(){ForeSee.controller.run(false)
};ForeSee.accepted=3Dfunction(A){ForeSee.language(A);ForeSee.controller.a=
ccepted();ForeSee.idhtml.hide()
};ForeSee.declined=3Dfunction(A){ForeSee.language(A);ForeSee.controller.d=
eclined();ForeSee.idhtml.hide()
};ForeSee.qualified=3Dfunction(){ForeSee.controller.qualified();ForeSee.i=
dhtml.hide()};ForeSee.language=3Dfunction(A){if(!A){return=20
}ForeSee.locale=3DA;ForeSee.c().fsr$set("locale",A)};ForeSee.qualify=3Dfu=
nction(A){ForeSee.canceled=3Dfalse;
ForeSee.qid=3DA};ForeSee.cancel=3Dfunction(){ForeSee.canceled=3Dtrue};For=
eSee.canceled=3Dfalse;ForeSee.SurveyController=3Dnew =
ForeSee.Class({Implements:ForeSee.Options,options:{},initialize:function(=
A){this.setOptions(A);
ForeSee.controller=3Dthis},load:function(){if(!ForeSee.enabled){return =
}var A=3Dthis;new =
ForeSee.Asset.javascript(ForeSee.files+"foresee-surveydef.js",{id:"forese=
e-surveydef",onload:function(){fsr$dbug.log("Survey Definitions =
Loaded.");
A.run(true)}})},run:function(B){if(B){if(!this.init()){return =
}}if(!this.setup()){return }var A;this.sd=3Dthis.pd;
this.sdi=3Dthis.pdi;A=3Dthis.process();if(A){return =
}this.sd=3Dthis.cd;this.sdi=3Dthis.cdi;A=3Dthis.process()
},process:function(){if(!this.sd){return =
false}if(this.sd.type=3D=3D"current"){ForeSee.c().fsr$set("sd",{name:this=
.sd.name,idx:this.sd.idx})
}if(this.processTracker()){var =
A=3Dthis;(function(){A.launch("tracker")}).fsr$delay(1);return =
true}if(this.processInvite()){var A=3Dthis;
(function(){A.launch("invite")}).fsr$delay(1);return =
true}if(this.sd.type=3D=3D"current"){this.setupLinks("pop",this.popLink);=

this.setupLinks("cancel",this.cancelTracker)}return =
false},processTracker:function(){if(!this.shouldTrack()){return false
}if(!this.trackerRunning()){return false}return =
true},shouldTrack:function(){if(!this.sd.ls){return false
}if(this.sd.type=3D=3D"previous"){if(!(this.sd.pop.when=3D=3D"later")||!(=
this.sd.pop.after=3D=3D"leaving-section")){return false
}}else{if(this.sd.type=3D=3D"current"){if(!(this.sd.pop.when=3D=3D"now"))=
{return false}}}return =
true},trackerRunning:function(){if(ForeSee.$defined(ForeSee.c().fsr$get("=
tracker"))){return true
}return false},processInvite:function(){if(this.inviteShown()){return =
false}if(!this.shouldInvite()){return false
}return true},shouldInvite:function(){if(this.sd.exclude){var =
C=3Dfalse;if(!C){C=3Dthis.checkExclude(this.sd.exclude.local||[],document=
.location.href)
}if(!C){C=3Dthis.checkExclude(this.sd.exclude.referer||[],document.locati=
on.referer)}if(C){var D=3DForeSee.c();
var =
B=3DD.fsr$get("ec");this.sd.ec=3DB[this.sd.name]=3DB[this.sd.name]+1;D.fs=
r$set("ec",B);return false
}}var =
A=3D(this.sd.type=3D=3D"previous")?"onexit":"onentry";if(this.sd.invite.w=
hen!=3DA){return false}if(!this.sd.ls){return false
}if(!(this.sd.sv>0&&this.sd.sv<=3Dthis.sd.criteria.sp)){return =
false}return =
true},inviteShown:function(){if(ForeSee.$defined(ForeSee.c().fsr$get("inv=
ite"))){return true
}return =
false},inviteAccepted:function(){if(ForeSee.c().fsr$get("invite")=3D=3D1)=
{return true}return false
},surveyShown:function(){if(ForeSee.$defined(ForeSee.c().fsr$get("survey"=
))){return true}return false
},launch:function(A){if(A=3D=3D"invite"){this.attemptInvite()}else{if(A=3D=
=3D"tracker"){this.popImmediate()
}}},checkExclude:function(C,B){for(var =
A=3D0,D=3DC.length;A<D;A++){if(B.match(C[A])){return true}}return false
},attemptInvite:function(){var A=3Dthis;var =
B=3D"invite";if(ForeSee.$P().mode=3D=3D"hybrid"){B=3D"checkDomain"
}new =
ForeSee.Service({host:ForeSee.services.check.host,path:ForeSee.services.c=
heck.path,url:ForeSee.services.check.url,success:ForeSee.services.check.s=
uccess,onSuccess:function(){A[B]()
},onFailure:function(){},onError:function(){A[B]()}}).ping()},checkDomain=
:function(){var A=3Dthis;var B=3D"invite";
new =
ForeSee.Service({host:ForeSee.services.domain.host,path:ForeSee.services.=
domain.path,url:ForeSee.services.domain.url,params:{"do":0},success:ForeS=
ee.services.check.success,onSuccess:function(){A[B]()
},onFailure:function(){}}).ping()},setupLinks:function(F,E){if(!this.sd.l=
inks){return }var =
G=3D0;if(this.inviteAccepted()&&!this.surveyShown()){var =
C=3Dthis.sd.links[F]||[];
for(var D=3D0,B=3DC.length;D<B;D++){var =
A=3Dthis.link(C[D].tag,C[D].attribute,C[D].patterns||[],C[D].qualifier,E)=
;
G=3DG+A}}fsr$dbug.log("linked ("+F+"): =
"+G)},link:function(A,F,E,D,C){var B=3Dthis;var =
G=3D0;$$fsr(A).fsr$each(function(J){for(var I=3D0,H=3DE.length;
I<H;I++){if(J[F].match(E[I])){G++;J.fsr$addEvents({click:function(){if(D)=
{ForeSee.qualify(D)}C.call(B)
}});break}}});return =
G},init:function(){fsr$dbug.log("=3D=3D=3D=3D=3D=3DINIT=3D=3D=3D=3D=3D=3D=
=3D");var E=3DForeSee.c();var H=3DE.fsr$get("alive")||0;
H=3DH+1;E.fsr$set("alive",H);this.ralive=3Dtrue;E=3DForeSee.c();H=3DE.fsr=
$get("alive");if(!H){fsr$dbug.log("Exit...cookies are not enabled.");
return false}fsr$dbug.log("alive: =
"+H);if(!this.trackerRunning()){clearInterval(fsr$timer);ForeSee.Cookie.d=
ispose("foresee.alive",{path:"/",domain:ForeSee.site.domain||false})
}var =
C=3DForeSee.Browser;E.fsr$set("browser",{name:ForeSee.Browser.Type.name,v=
ersion:ForeSee.Browser.Type.version,platform:ForeSee.Browser.Platform.os}=
);
fsr$dbug.log("browser: "+C.Type.name+" "+C.Type.version+" on =
"+C.Platform.os);if(ForeSee.UnsupportedBrowsers[C.Type.name]){if(C.Type.v=
ersion<=3DForeSee.UnsupportedBrowsers[C.Type.name]){fsr$dbug.log("Browser=
 not surpported.");
return false}}if(this.validateIP()=3D=3D0){fsr$dbug.log("Invalid IP =
Address.");return false}var =
G;if(ForeSee.$defined(E.fsr$get("finish"))){var =
D=3DE.fsr$get("timeout");
var =
A=3D((ForeSee.$time()-E.fsr$get("finish"))/1000);fsr$dbug.log("ptimeout: =
"+D);fsr$dbug.log("loadtime: "+A);
G=3D(0.9*D)+(0.1*(A*2));if(G<2){G=3D2}else{if(G>5){G=3D5}}}else{G=3DForeS=
ee.$P().tracker.timeout}E.fsr$set("timeout",G);
fsr$dbug.log("timeout: "+G);if(!E.fsr$get("start")){var =
J;if(J=3DForeSee.Cookie.read("foresee.repeatdays",{path:"/",domain:ForeSe=
e.site.domain||false})){fsr$dbug.log("Persistent Cookie Found: "+J);
return false}E.fsr$set("start",ForeSee.$time());var =
I=3Dthis;this.dhtml_win=3D1;new =
ForeSee.Asset.javascript(ForeSee.files+"foresee-dhtml-popup.js",{id:"fore=
see-dhtml-popup",onload:function(){fsr$dbug.log("DHTML popup script =
loaded (1).");
I.dhtml_win=3D2}});this.dhtml_css=3D1;new =
ForeSee.Asset.css(ForeSee.files+ForeSee.$P().invite.css);if(ForeSee.$P().=
invite.css!=3DForeSee.$P().qualifier.css){new =
ForeSee.Asset.css(ForeSee.files+ForeSee.$P().qualifier.css)
}this.dhtml_css=3D2;this.generateid();if(document.referrer&&document.refe=
rrer!=3D""){var =
F=3Ddocument.referrer.match(/^(\w+\:\/\/)?(((\w+\.?))+)\//)[2];
E.fsr$set("referrer",F);fsr$dbug.log("referrer: "+F);var =
B=3Dthis.decodeReferrer(document.referrer);
E.fsr$set("terms",B);fsr$dbug.log("search terms: =
"+B)}}fsr$dbug.log("invite: "+(ForeSee.$pick(E.fsr$get("invite"),"")));
fsr$dbug.log("tracker: =
"+(ForeSee.$pick(E.fsr$get("tracker")||"")));return =
true},setup:function(){fsr$dbug.log("=3D=3D=3D=3D=3D=3DSETUP=3D=3D=3D=3D=3D=
=3D=3D");
var M=3DForeSee.c();var =
P=3DM.fsr$get("pv")?M.fsr$get("pv")+1:1;M.fsr$set("pv",P);fsr$dbug.log("p=
v: "+P);
ForeSee.sv=3DForeSee.$random(0,100);this.sp=3Dnew =
ForeSee.Hash.Cookie("foresee.sp",{path:"/",domain:ForeSee.site.domain||fa=
lse});
var =
K,N,E,A,F,Q;A=3DM.fsr$get("current");Q=3DM.fsr$get("cdi");K=3Ddocument.lo=
cation.href;M.fsr$set("current",K);
this.language();if(ForeSee.locale){fsr$dbug.log("language: =
"+ForeSee.locale||"")}this.subsite();if(ForeSee.subsite){M.fsr$set("site"=
,ForeSee.subsite||"");
fsr$dbug.log("site: "+ForeSee.subsite||"")}var =
D=3DM.fsr$get("lc")||{};var I=3DM.fsr$get("ls")||{};var =
J=3DM.fsr$get("ec")||{};
N=3Dthis.match(K);if(N.length!=3D0){fsr$dbug.log("=3D=3D=3DCURRENT=3D=3D=3D=
=3D");for(var H=3D0,G=3DN.length;H<G;H++){var =
L=3DForeSee.surveydefs[N[H]];
L.idx=3DN[H];this.criteria(L.criteria);L.lc=3DD[L.name]=3DD[L.name]?D[L.n=
ame]+1:1;L.ec=3DJ[L.name]=3DJ[L.name]?J[L.name]:0;
L.type=3D"current";this.configLoyalty(L);var B=3Dthis.loyaltyDef(L);var =
O=3Dthis.checkLoyalty(B,L.lc,L.ec);
if(O>-1){L.ls=3DI[L.name]=3Dtrue;if(ForeSee.$type(L.criteria.lf)=3D=3D"ar=
ray"){L.criteria.lf=3DL.criteria.lf[O];
L.criteria.sp=3DL.criteria.sp[O];L.pop.when=3DL.pop.when[O]}if(L.pin){var=
 C=3DM.fsr$get("p")||{};C[L.name]=3D1;
M.fsr$set("p",C)}}else{L.ls=3DI[L.name]=3Dfalse;if(ForeSee.$type(L.criter=
ia.lf)=3D=3D"array"){L.criteria.lf=3DL.criteria.lf[0];
L.criteria.sp=3DL.criteria.sp[0];L.pop.when=3DL.pop.when[0]}}this.configu=
re(L);E=3DL.idx;M.fsr$set("cdi",L.idx);
this.cd=3DL;break}M.fsr$set("lc",D);M.fsr$set("ls",I);M.fsr$set("ec",J)}i=
f(ForeSee.$defined(Q)&&(Q!=3DE)){fsr$dbug.log("=3D=3D=3DPREVIOUS=3D=3D=3D=
");
var =
L=3DForeSee.surveydefs[Q];L.idx=3DQ;this.criteria(L);L.lc=3DD[L.name];L.l=
s=3DI[L.name]||false;L.type=3D"previous";
this.configLoyalty(L);this.configure(L);this.pd=3DL}if(!this.cd&&!this.pd=
){return false}return true
},configLoyalty:function(A){if(ForeSee.$type(A.criteria.lf)=3D=3D"number"=
){A.criteria.lf=3D{v:A.criteria.lf,o:">=3D"}
}},loyaltyDef:function(B){var =
A=3DB.criteria.lf;if(ForeSee.$type(B.criteria.lf)=3D=3D"object"){A=3D[B.c=
riteria.lf]
}return A},checkLoyalty:function(E,F,C){var B=3D-1;for(var =
D=3D0,A=3DE.length;D<A;D++){if(E[D].o=3D=3D">=3D"){if(F>=3DE[D].v){B=3DD
}}else{if(E[D].o=3D=3D"=3D"){if((F-C)=3D=3DE[D].v){B=3DD}}else{if(E[D].o=3D=
=3D">"){if(F>E[D].v){B=3DD}}}}}return B},validateIP:function(D){var =
A=3D1;
var C=3DForeSee.$P().ipexclude;if(!C){return A}var =
D;if(C.src=3D=3D"cookie"){if(C.type&&C.type=3D=3D"client"){D=3DForeSee.Co=
okie.read(C.name,{path:"/",domain:ForeSee.site.domain||false})
}else{D=3DForeSee.c().fsr$get("ip")}}else{if(C.src=3D=3D"variable"){if(C.=
type&&C.type=3D=3D"client"){D=3Dwindow[C.name]
}else{D=3DForeSee[C.name]}}}D=3DD||"";for(var =
B=3D0;B<C.ips.length;B++){if(D.match(C.ips[B])){A=3D0;break
}}return A},configure:function(C){var E=3DForeSee.c();fsr$dbug.log("sid: =
"+C.name);fsr$dbug.log("lc: "+C.lc);
fsr$dbug.log("lf: "+C.criteria.lf.v+" ("+C.criteria.lf.o+") =
");C.sv=3DForeSee.sv;fsr$dbug.log("sv: "+C.sv);
if(ForeSee.$type(C.criteria.sp)=3D=3D"array"){C.criteria.sp=3DC.criteria.=
sp[(new Date()).getDay()]}var =
A=3D(!ForeSee.locale)?C.name:C.name+"-"+ForeSee.locale;
C.criteria.sp=3Dthis.sp.fsr$get(A)||this.sp.fsr$get(C.name)||C.criteria.s=
p;fsr$dbug.log("sp: "+C.criteria.sp);
if(C.invite){C.invite=3DForeSee.$merge(ForeSee.$P().invite,C.invite)}C.tr=
acker=3DForeSee.$merge(ForeSee.$P().tracker,C.tracker);
C.survey=3DForeSee.$merge(ForeSee.$P().survey,C.survey);C.qualifier=3DFor=
eSee.$merge(ForeSee.$P().qualifier,C.qualifier);
C.cancel=3DForeSee.$merge(ForeSee.$P().cancel,C.cancel);C.pop=3DForeSee.$=
merge(ForeSee.$P().pop,C.pop);
C.repeatdays=3DForeSee.$pick(ForeSee.$P().repeatdays,C.repeatdays);var =
B=3D[].fsr$extend(ForeSee.$P().exclude.local);
if(C.exclude&&C.exclude.local){C.exclude.local=3DB.fsr$extend(C.exclude.l=
ocal)}else{if(C.exclude){C.exclude.local=3DB
}else{C.exclude=3D{local:B}}}var =
D=3D[].fsr$extend(ForeSee.$P().exclude.referer);if(C.exclude&&C.exclude.r=
eferer){C.exclude.referer=3DD.fsr$extend(C.exclude.referer)
}else{if(C.exclude){C.exclude.referer=3DD}else{C.exclude=3D{referer:D}}}}=
,unload:function(){if(!ForeSee.enabled){return=20
}if(!this.runload&&this.ralive){this.runload=3Dtrue;this.uninit()}return =
},uninit:function(){var B=3DForeSee.c();
var =
A=3DB.fsr$get("alive")||0;B.fsr$set("alive",(A-1)>0?A-1:0);B.fsr$set("pre=
vious",B.fsr$get("current"));
B.fsr$set("finish",ForeSee.$time())},match:function(A){if(!A){return =
}var E=3D[];var K=3DForeSee.surveydefs;
var B=3DForeSee.c().fsr$get("p")||{};for(var =
J=3D0,C,F=3DK.length,G=3D0;J<F;J++){var I=3DG;C=3DK[J].include.urls||[];
for(var =
H=3D0,D=3DC.length;H<D;H++){if(A.match(C[H])){E[G++]=3DJ;break}}if(G!=3DI=
){break}C=3DK[J].include.cookies||[];
for(var H=3D0,D=3DC.length;H<D;H++){var =
L;if(L=3DForeSee.Cookie.read(C[H].name,{path:C[H].path||false,domain:C[H]=
.domain||false})){if(L.match(C[H].value||".")){E[G++]=3DJ;
break}}}if(G!=3DI){break}C=3DK[J].include.variables||[];for(var =
H=3D0,D=3DC.length;H<D;H++){var =
L;if(L=3Dwindow[C[H].name]){if(L.match(C[H].value)){E[G++]=3DJ;
break}}}if(G!=3DI){break}if(B[K[J].name]){E[G++]=3DJ}if(G!=3DI){break}}re=
turn E},invite:function(){ForeSee.invite=3D0;
if(ForeSee.locale){ForeSee.c().fsr$set("locale",ForeSee.locale)}var =
A=3Dthis;if(this.sd.invite){(function(){ForeSee.log("Invite =
Shown",ForeSee.c().fsr$get("current"));
A.prepareDHTML("invite","accepted","declined","closed")}).fsr$delay((this=
.sd.invite.delay||0)*1000)
}else{(function(){A.accepted();A.close()}).fsr$delay(0)}},prepareDHTML:fu=
nction(E,F,C,B){var A=3Dthis;
if(!ForeSee.$defined(this.dhtml_css)){this.dhtml_css=3D1;new =
ForeSee.Asset.css(ForeSee.files+this.sd[E].css);
this.dhtml_css=3D2}if(!ForeSee.$defined(this.dhtml_win)){this.dhtml_win=3D=
1;new =
ForeSee.Asset.javascript(ForeSee.files+"foresee-dhtml-popup.js",{id:"fore=
see-dhtml-popup",onload:function(){fsr$dbug.log("DHTML popup script =
loaded (2).");
A.dhtml_win=3D2;A.showDHTML(E,F,C,B)}})}else{if(this.dhtml_win=3D=3D1){va=
r D=3D(function(){if(A.dhtml_win=3D=3D1){return=20
}ForeSee.$clear(D);A.showDHTML(E,F,C,B)}).fsr$periodical(50)}else{if(this=
.dhtml_win=3D=3D2){(function(){A.showDHTML(E,F,C,B)
}).fsr$delay(1)}}}},showDHTML:function(E,B,G,C){var H=3Dthis;var =
F=3Dthis.sd[E].dhtml||this.sd[E];this.page(F);
var D=3D[];if(F.buttons){var =
A=3D0;if(F.buttons.accept){D[A]=3D{properties:{id:"accept"},style:"fsr_bu=
tton fsr_accept",text:F.buttons.accept,onClick:function(){H[B]()
},onMouseover:function(){this.className=3D"fsr_closeSticky fsr_button =
fsr_mouseover_accept"},onMouseout:function(){this.className=3D"fsr_closeS=
ticky fsr_button fsr_accept"
}};A++}if(F.buttons.decline){D[A]=3D{properties:{id:"decline"},style:"fsr=
_button fsr_decline",text:F.buttons.decline,onClick:function(){H[G]()
},onMouseover:function(){this.className=3D"fsr_closeSticky fsr_button =
fsr_mouseover_decline"},onMouseout:function(){this.className=3D"fsr_close=
Sticky fsr_button fsr_decline"
}};A++}}var =
J=3D{position:{x:F.x,y:F.y},wrapWithUi:true,uiOptions:{width:F.width+"px"=
,baseHref:ForeSee.files,buttons:D},modalOptions:{modalStyle:{"background-=
color":F.bgcolor,opacity:F.opacity},hideOnClick:F.hideOnClick},requestOpt=
ions:{evalScripts:true}};
ForeSee[E]=3D0;var I;if(F.content){J.content=3DF.content;I=3Dnew =
ForeSee.StickyWinModal(J)}else{J.url=3DForeSee.files+(F.url.dhtml||F.url)=
;
I=3Dnew =
ForeSee.StickyWinModal.Ajax(J)}if(C){I.fsr$addEvent("onClose",function(){=
H[C]()})}if(F.content){I.show()
}else{I.update()}ForeSee.idhtml=3DI},accepted:function(){ForeSee.invite=3D=
1;ForeSee.log("Invite Accepted");
ForeSee.c().fsr$set("invite",ForeSee.invite);if(ForeSee.$P().mode=3D=3D"h=
ybrid"){new =
ForeSee.Service({host:ForeSee.services.domain.host,path:ForeSee.services.=
domain.path,url:ForeSee.services.domain.url,params:{"do":1,rw:this.sd.rep=
eatdays*24*60}}).ping()
}var =
A=3Dthis;A.processAccept()},declined:function(){ForeSee.invite=3D-1;ForeS=
ee.log("Invite Declined");
ForeSee.c().fsr$set("invite",ForeSee.invite)},closed:function(){ForeSee.c=
().fsr$set("invite",ForeSee.invite);
if(this.sd.repeatdays){ForeSee.Cookie.write("foresee.repeatdays",this.sd.=
repeatdays,{path:"/",domain:ForeSee.site.domain||false,duration:this.sd.r=
epeatdays})
}},qualified:function(){ForeSee.qualifier=3D1;ForeSee.log("Qualifier =
Accepted");ForeSee.c().fsr$set("qualifier",ForeSee.qualifier);
var =
A=3Dthis;A.processQualifier()},processAccept:function(){if(this.sd.pop.wh=
en=3D=3D"later"){if(this.sd.pop.tracker){this.popTracker()
}this.setupLinks("pop",this.popLink);this.setupLinks("cancel",this.cancel=
Tracker)}else{if(this.sd.pop.when=3D=3D"now"){ForeSee.c().fsr$set("survey=
",1);
if(!this.sd.pop.what!=3D"qualifier"){this.popSurvey()}else{this.popQualif=
ier()}}else{if(this.sd.pop.when=3D=3D"both"){this.popTracker();
this.popSurvey()}}}},processQualifier:function(){if(!ForeSee.canceled){th=
is.popSurvey()}else{this.popCancel()
}},popImmediate:function(){var A=3Dthis;var =
B=3DForeSee.c();if(this.trackerRunning()){if(ForeSee.Browser.Type.name!=3D=
"Firefox"||!this.sd.qualifier){B.fsr$set("force",1)
}else{this.cancelTracker();(function(){ForeSee.log("Qualifier =
Shown",ForeSee.c().fsr$get("current"));
A.prepareDHTML("qualifier","qualified")}).fsr$delay((this.sd.qualifier.de=
lay||0)*1000)}}else{ForeSee.c().fsr$set("survey",1);
if(!this.sd.pop.what!=3D"qualifier"){this.popSurvey()}else{this.popQualif=
ier()}}},popSurvey:function(){var B=3Dthis.sd.survey;
var =
A=3Dthis.sd.pop;this.popMain(this.sid(),B.width,B.height,A.pu,B.loading,"=
Survey")},popFeedback:function(){var C=3DForeSee.$P();
var B=3D"feedback";var =
A=3DForeSee.locale;if(A){B=3DB+"-"+A}this.popMain(B,C.survey.width,C.surv=
ey.height,false,C.survey.loading,"Feedback")
},popMain:function(C,B,I,J,D,H){var G=3DForeSee.services.survey;var =
E=3Dnew =
ForeSee.Hash({sid:C,cid:ForeSee.id,version:ForeSee.version}).fsr$toQueryS=
tring();
var F=3DForeSee.CPPS.fsr$toQueryString();var =
A=3Ddocument.location.protocol+"//"+G.host+G.path+G.url+"?"+E+"&"+F;
if(D){this.page(ForeSee.$P().loading);A=3DForeSee.files+ForeSee.$P().load=
ing.url+"?url=3D"+A}this.pop("fsr"+H,A,(window.screen.width-B)/2,(window.=
screen.height-I)/2,B,I,J);
ForeSee.log(H+" =
Shown",ForeSee.c().fsr$get("current"))},popTracker:function(){fsr$timer=3D=
setInterval(fsr$setAlive,1000);
this.popOther(this.sd.tracker,true,"Tracker")},popQualifier:function(){th=
is.popOther(this.sd.qualifier,this.sd.pop.pu,"Qualifier")
},popCancel:function(){this.popOther(this.sd.cancel,false,"Cancel")},popL=
ink:function(){if(!this.surveyShown()){this.popImmediate()
}},cancelTracker:function(){if(this.trackerRunning()){var =
A=3Dwindow.open("","fsrTracker");if(A){A.close()
}}},popOther:function(F,C,B){this.page(F);var =
E=3D(window.screen.width-F.width)/2;var =
D=3D(window.screen.height-F.height)/2;
var =
A=3DForeSee.files+(F.url.pop||F.url)+"?siteid=3D"+ForeSee.siteid+"&sdidx=3D=
"+this.sd.idx;this.pop("fsr"+B,A,E,D,F.width,F.height,C);
ForeSee.log(B+" =
Shown",ForeSee.c().fsr$get("current"))},sid:function(){var =
E=3DForeSee.c();var C=3Dthis.sd.name;
var A=3Dthis.sd.pop.now;if(A){C=3DC+"-"+A}var =
D=3DForeSee.qid;if(D){C=3DC+"-"+D}var B=3DE.fsr$get("locale");
if(B){C=3DC+"-"+B}return =
C},pop:function(D,C,H,G,F,A,E,B){(function(){new =
ForeSee.Browser.Popup(C,{name:D,toolbar:0,location:0,directories:0,status=
:0,scrollbars:1,resizable:1,width:F,height:A,x:H,y:G,blur:E,menubar:0})
}).fsr$delay(B||0)},language:function(){var =
F=3DForeSee.$P().language;if(!F){return }var B=3DF.locale;
var =
E;if(F.src=3D=3D"location"){E=3Ddocument.location.href}else{if(F.src=3D=3D=
"cookie"){if(F.type&&F.type=3D=3D"client"){E=3DForeSee.Cookie.read(F.name=
,{path:"/",domain:ForeSee.site.domain||false})
}else{E=3DForeSee.c().fsr$get("lang")}}else{if(F.src=3D=3D"variable"){if(=
F.type&&F.type=3D=3D"client"){E=3Dwindow[F.name]
}else{E=3DForeSee[F.name]}}}}E=3DE||"";var C=3DF.locales||[];for(var =
D=3D0,A=3DC.length;D<A;D++){if(E.match(C[D].match)){B=3DC[D].locale;
break}}ForeSee.locale=3DB},page:function(E){var =
B=3DForeSee.c().fsr$get("locale");if(!B){return }var D=3DE.locales||[];
for(var =
C=3D0,A=3DD.length;C<A;C++){if(D[C].locale=3D=3DB){if(D[C].url){E.url=3DD=
[C].url}if(D[C].content){E.content=3DD[C].content
}if(D[C].buttons){E.buttons=3D{accept:"",decline:""};if(D[C].buttons.acce=
pt){E.buttons.accept=3DD[C].buttons.accept
}if(D[C].buttons.decline){E.buttons.decline=3DD[C].buttons.decline}}if(D[=
C].width){E.width=3DD[C].width
}if(D[C].height){E.height=3DD[C].height}break}}},criteria:function(E){var=
 B=3DForeSee.locale;if(!B){return=20
}var D=3DE.locales||[];for(var =
C=3D0,A=3DD.length;C<A;C++){if(D[C].locale=3D=3DB){E.sp=3DD[C].sp;E.lf=3D=
D[C].lf;
break}}},subsite:function(){var D=3DForeSee.$P().subsites;if(!D){return =
}var C=3Ddocument.location.href;
var A=3D-1;for(var =
B=3D0,E=3DD.length;B<E;B++){if(C.match(D[B])){A=3DB;ForeSee.subsite=3DD[B=
];break}}if(A=3D=3D-1){ForeSee.subsite=3D"other"
}},generateid:function(){if(!ForeSee.services.event.enabled){return }new =
ForeSee.RemoteEvent("getsessionid",{host:ForeSee.services.event.host,path=
:ForeSee.services.event.path,url:ForeSee.services.event.url}).send({id:"f=
oresee."+ForeSee.site.cookie})
},decodeReferrer:function(A){A=3DdecodeURIComponent(A);var C=3Dnull;var =
B=3Ddocument.referrer.match(/[?&]q=3D([^&]*)/)||document.referrer.match(/=
[?&]p=3D([^&]*)/)||document.referrer.match(/[?&]query=3D([^&]*)/);
if(!B){return }var C=3Dunescape(B[1]);if(C){C=3DC.replace(/\+/g," =
")}return C}});new ForeSee.SurveyController({});
window.fsr$addEvent("domready",function(){(function(){ForeSee.controller.=
load()}).fsr$delay(1)});
window.fsr$addEvent("unload",function(){ForeSee.controller.unload()});
------=_NextPart_000_0000_01CA4DB9.92AFE550
Content-Type: application/octet-stream
Content-Transfer-Encoding: quoted-printable
Content-Location: http://www.fmcsa.dot.gov/include/Webtrends.js

=EF=BB=BF
/*webtrends code starts*/
/* START OF SmartSource Data Collector TAG -->
<!-- Copyright (c) 1996-2007 WebTrends Inc.  All rights reserved. -->
<!-- V8.0 -->
<!-- $DateTime: 2007/03/01 14:48:29 $ -->*/

//  <script type=3D"text/javascript">
var gDomain =3D "wt.fmcsa.dot.gov";
var gDcsId =3D "dcs5w0txb10000wocrvqy1nqm_6n1p";


if ((typeof (gConvert) !=3D "undefined") && gConvert && =
(document.cookie.indexOf(gFpc + "=3D") =3D=3D -1) && =
(document.cookie.indexOf("WTLOPTOUT=3D") =3D=3D -1)) {
    document.write("<SCR" + "IPT TYPE=3D'text/javascript' SRC=3D'" + =
"http" + (window.location.protocol.indexOf('https:') =3D=3D 0 ? 's' : =
'') + "://" + gDomain + "/" + gDcsId + "/wtid.js" + "'><\/SCR" + =
"IPT>");
}

// </script>

//  <script type=3D"text/javascript">
var gImages =3D new Array;
var gIndex =3D 0;
var DCS =3D new Object();
var WT =3D new Object();
var DCSext =3D new Object();
var gQP =3D new Array();
var gI18n =3D false;
if (window.RegExp) {
    var RE =3D gI18n ? { "%25": /\%/g} : { "%09": /\t/g, "%20": / /g, =
"%23": /\#/g, "%26": /\&/g, "%2B": /\+/g, "%3F": /\?/g, "%5C": /\\/g, =
"%22": /\"/g, "%7F": /\x7F/g, "%A0": /\xA0/g };
    if (gI18n) {
        var EXRE =3D =
/dcs(uri)|(ref)|(aut)|(met)|(sta)|(sip)|(pro)|(byt)|(dat)|(p3p)|(cfg)|(re=
direct)|(cip)/i;
    }
}

// Add customizations here

function dcsVar() {
    var dCurrent =3D new Date();
    WT.tz =3D dCurrent.getTimezoneOffset() / 60 * -1;
    if (WT.tz =3D=3D 0) {
        WT.tz =3D "0";
    }
    WT.bh =3D dCurrent.getHours();
    WT.ul =3D navigator.appName =3D=3D "Netscape" ? navigator.language : =
navigator.userLanguage;
    if (typeof (screen) =3D=3D "object") {
        WT.cd =3D navigator.appName =3D=3D "Netscape" ? =
screen.pixelDepth : screen.colorDepth;
        WT.sr =3D screen.width + "x" + screen.height;
    }
    if (typeof (navigator.javaEnabled()) =3D=3D "boolean") {
        WT.jo =3D navigator.javaEnabled() ? "Yes" : "No";
    }
    if (document.title) {
        WT.ti =3D document.title;
    }
    WT.js =3D "Yes";
    WT.jv =3D dcsJV();
    if (document.body && document.body.addBehavior) {
        document.body.addBehavior("#default#clientCaps");
        WT.ct =3D document.body.connectionType || "unknown";
        document.body.addBehavior("#default#homePage");
        WT.hp =3D document.body.isHomePage(location.href) ? "1" : "0";
    }
    else {
        WT.ct =3D "unknown";
    }
    if (parseInt(navigator.appVersion) > 3) {
        if ((navigator.appName =3D=3D "Microsoft Internet Explorer") && =
document.body) {
            WT.bs =3D document.body.offsetWidth + "x" + =
document.body.offsetHeight;
        }
        else if (navigator.appName =3D=3D "Netscape") {
            WT.bs =3D window.innerWidth + "x" + window.innerHeight;
        }
    }
    WT.fi =3D "No";
    if (window.ActiveXObject) {
        for (var i =3D 10; i > 0; i--) {
            try {
                var flash =3D new =
ActiveXObject("ShockwaveFlash.ShockwaveFlash." + i);
                WT.fi =3D "Yes";
                WT.fv =3D i + ".0";
                break;
            }
            catch (e) {
            }
        }
    }
    else if (navigator.plugins && navigator.plugins.length) {
        for (var i =3D 0; i < navigator.plugins.length; i++) {
            if (navigator.plugins[i].name.indexOf('Shockwave Flash') =
!=3D -1) {
                WT.fi =3D "Yes";
                WT.fv =3D navigator.plugins[i].description.split(" =
")[2];
                break;
            }
        }
    }
    if (gI18n) {
        WT.em =3D (typeof (encodeURIComponent) =3D=3D "function") ? =
"uri" : "esc";
        if (typeof (document.defaultCharset) =3D=3D "string") {
            WT.le =3D document.defaultCharset;
        }
        else if (typeof (document.characterSet) =3D=3D "string") {
            WT.le =3D document.characterSet;
        }
    }
    WT.tv =3D "8.0.3";
    WT.sp =3D "@@SPLITVALUE@@";
    DCS.dcsdat =3D dCurrent.getTime();
    DCS.dcssip =3D window.location.hostname;
    DCS.dcsuri =3D window.location.pathname;
    if (window.location.search) {
        DCS.dcsqry =3D window.location.search;
        if (gQP.length > 0) {
            for (var i =3D 0; i < gQP.length; i++) {
                var pos =3D DCS.dcsqry.indexOf(gQP[i]);
                if (pos !=3D -1) {
                    var front =3D DCS.dcsqry.substring(0, pos);
                    var end =3D DCS.dcsqry.substring(pos + =
gQP[i].length, DCS.dcsqry.length);
                    DCS.dcsqry =3D front + end;
                }
            }
        }
    }
    if ((window.document.referrer !=3D "") && (window.document.referrer =
!=3D "-")) {
        if (!(navigator.appName =3D=3D "Microsoft Internet Explorer" && =
parseInt(navigator.appVersion) < 4)) {
            DCS.dcsref =3D window.document.referrer;
        }
    }
}

function dcsA(N, V) {
    if (gI18n && !EXRE.test(N)) {
        if (N =3D=3D "dcsqry") {
            var newV =3D "";
            var params =3D V.substring(1).split("&");
            for (var i =3D 0; i < params.length; i++) {
                var pair =3D params[i];
                var pos =3D pair.indexOf("=3D");
                if (pos !=3D -1) {
                    var key =3D pair.substring(0, pos);
                    var val =3D pair.substring(pos + 1);
                    if (i !=3D 0) {
                        newV +=3D "&";
                    }
                    newV +=3D key + "=3D" + dcsEncode(val);
                }
            }
            V =3D V.substring(0, 1) + newV;
        }
        else {
            V =3D dcsEncode(V);
        }
    }
    return "&" + N + "=3D" + dcsEscape(V, RE);
}

function dcsEscape(S, REL) {
    if (typeof (REL) !=3D "undefined") {
        var retStr =3D new String(S);
        for (var R in REL) {
            retStr =3D retStr.replace(REL[R], R);
        }
        return retStr;
    }
    else {
        return escape(S);
    }
}

function dcsEncode(S) {
    return (typeof (encodeURIComponent) =3D=3D "function") ? =
encodeURIComponent(S) : escape(S);
}

function dcsCreateImage(dcsSrc) {
    if (document.images) {
        gImages[gIndex] =3D new Image;
        gImages[gIndex].src =3D dcsSrc;
        gIndex++;
    }
    else {
        document.write('<IMG ALT=3D"" BORDER=3D"0" NAME=3D"DCSIMG" =
WIDTH=3D"1" HEIGHT=3D"1" SRC=3D"' + dcsSrc + '">');
    }
}

function dcsMeta() {
    var elems;
    if (document.all) {
        elems =3D document.all.tags("meta");
    }
    else if (document.documentElement) {
        elems =3D document.getElementsByTagName("meta");
    }
    if (typeof (elems) !=3D "undefined") {
        var length =3D elems.length;
        for (var i =3D 0; i < length; i++) {
            var name =3D elems.item(i).name;
            var content =3D elems.item(i).content;
            var equiv =3D elems.item(i).httpEquiv;
            if (name.length > 0) {
                if (name.indexOf("WT.") =3D=3D 0) {
                    WT[name.substring(3)] =3D content;
                }
                else if (name.indexOf("DCSext.") =3D=3D 0) {
                    DCSext[name.substring(7)] =3D content;
                }
                else if (name.indexOf("DCS.") =3D=3D 0) {
                    DCS[name.substring(4)] =3D content;
                }
            }
            else if (gI18n && (equiv =3D=3D "Content-Type")) {
                var pos =3D content.toLowerCase().indexOf("charset=3D");
                if (pos !=3D -1) {
                    WT.mle =3D content.substring(pos + 8);
                }
            }
        }
    }
}

function dcsTag() {
    if (document.cookie.indexOf("WTLOPTOUT=3D") !=3D -1) {
        return;
    }
    var P =3D "http" + (window.location.protocol.indexOf('https:') =
=3D=3D 0 ? 's' : '') + "://" + gDomain + (gDcsId =3D=3D "" ? '' : '/' + =
gDcsId) + "/dcs.gif?";
    for (var N in DCS) {
        if (DCS[N]) {
            P +=3D dcsA(N, DCS[N]);
        }
    }
    var keys =3D ["co_f", "vt_sid", "vt_f_tlv"];
    for (var i =3D 0; i < keys.length; i++) {
        var key =3D keys[i];
        if (WT[key]) {
            P +=3D dcsA("WT." + key, WT[key]);
            delete WT[key];
        }
    }
    for (N in WT) {
        if (WT[N]) {
            P +=3D dcsA("WT." + N, WT[N]);
        }
    }
    for (N in DCSext) {
        if (DCSext[N]) {
            P +=3D dcsA(N, DCSext[N]);
        }
    }
    if (P.length > 2048 && navigator.userAgent.indexOf('MSIE') >=3D 0) {
        P =3D P.substring(0, 2040) + "&WT.tu=3D1";
    }
    dcsCreateImage(P);
}

function dcsJV() {
    var agt =3D navigator.userAgent.toLowerCase();
    var major =3D parseInt(navigator.appVersion);

    var mac =3D (agt.indexOf("mac") !=3D -1);
    var ff =3D (agt.indexOf("firefox") !=3D -1);
    var ff0 =3D (agt.indexOf("firefox/0.") !=3D -1);
    var ff10 =3D (agt.indexOf("firefox/1.0") !=3D -1);
    var ff15 =3D (agt.indexOf("firefox/1.5") !=3D -1);
    var ff2up =3D (ff && !ff0 && !ff10 & !ff15);
    var nn =3D (!ff && (agt.indexOf("mozilla") !=3D -1) && =
(agt.indexOf("compatible") =3D=3D -1));
    var nn4 =3D (nn && (major =3D=3D 4));
    var nn6up =3D (nn && (major >=3D 5));
    var ie =3D ((agt.indexOf("msie") !=3D -1) && (agt.indexOf("opera") =
=3D=3D -1));
    var ie4 =3D (ie && (major =3D=3D 4) && (agt.indexOf("msie 4") !=3D =
-1));
    var ie5up =3D (ie && !ie4);
    var op =3D (agt.indexOf("opera") !=3D -1);
    var op5 =3D (agt.indexOf("opera 5") !=3D -1 || =
agt.indexOf("opera/5") !=3D -1);
    var op6 =3D (agt.indexOf("opera 6") !=3D -1 || =
agt.indexOf("opera/6") !=3D -1);
    var op7up =3D (op && !op5 && !op6);
    var jv =3D "1.1";
    if (ff2up) {
        jv =3D "1.7";
    }
    else if (ff15) {
        jv =3D "1.6";
    }
    else if (ff0 || ff10 || nn6up || op7up) {
        jv =3D "1.5";
    }
    else if ((mac && ie5up) || op6) {
        jv =3D "1.4";
    }
    else if (ie5up || nn4 || op5) {
        jv =3D "1.3";
    }
    else if (ie4) {
        jv =3D "1.2";
    }
    return jv;
}

function dcsFunc(func) {
    if (typeof (window[func]) =3D=3D "function") {
        window[func]();
    }
}

dcsVar();
dcsMeta();
dcsFunc("dcsAdv");
dcsTag();
//-->

//  </script>
/* END OF SmartSource Data Collector  TAG */

/* end webtrends code*/
------=_NextPart_000_0000_01CA4DB9.92AFE550
Content-Type: application/octet-stream
Content-Transfer-Encoding: quoted-printable
Content-Location: http://www.fmcsa.dot.gov/include/GovDelivery.js

=EF=BB=BF
//<!--Gov Delivery popup code begins -->
function popupGovDev(catid) {
    helpWindow =3D =
window.open('http://service.govdelivery.com/service/subscribe.html?code=3D=
USDOTFMCSA_' + catid, 'Popup', =
'width=3D600,height=3D560,left=3D380,top=3D150,toolbar=3Dno,scrollbars=3D=
yes,resizable=3Dyes,location=3Dno')
    helpWindow.focus()
}
function switch_lan() {
    window.document.frmLan.submit();
}
function DefaultText(txt, evt) {
    if (txt.value.length =3D=3D 0 && evt.type =3D=3D "blur") {
        txt.value =3D "Search All FMCSA Sites";
        //txt.style.color =3D "grey"
    }
    if (txt.value =3D=3D "Search All FMCSA Sites" && evt.type =3D=3D =
"focus") {
        txt.value =3D "";
    }
}
 //   <!--Gov Delivery popup code ends -->
------=_NextPart_000_0000_01CA4DB9.92AFE550
Content-Type: application/octet-stream
Content-Transfer-Encoding: quoted-printable
Content-Location: http://www.fmcsa.dot.gov/WebResource.axd?d=UPcOHGV5X_2qK7_t7-Ee3Q2&t=633338582618053248

function WebForm_PostBackOptions(eventTarget, eventArgument, validation, =
validationGroup, actionUrl, trackFocus, clientSubmit) {
    this.eventTarget =3D eventTarget;
    this.eventArgument =3D eventArgument;
    this.validation =3D validation;
    this.validationGroup =3D validationGroup;
    this.actionUrl =3D actionUrl;
    this.trackFocus =3D trackFocus;
    this.clientSubmit =3D clientSubmit;
}
function WebForm_DoPostBackWithOptions(options) {
    var validationResult =3D true;
    if (options.validation) {
        if (typeof(Page_ClientValidate) =3D=3D 'function') {
            validationResult =3D =
Page_ClientValidate(options.validationGroup);
        }
    }
    if (validationResult) {
        if ((typeof(options.actionUrl) !=3D "undefined") && =
(options.actionUrl !=3D null) && (options.actionUrl.length > 0)) {
            theForm.action =3D options.actionUrl;
        }
        if (options.trackFocus) {
            var lastFocus =3D theForm.elements["__LASTFOCUS"];
            if ((typeof(lastFocus) !=3D "undefined") && (lastFocus !=3D =
null)) {
                if (typeof(document.activeElement) =3D=3D "undefined") {
                    lastFocus.value =3D options.eventTarget;
                }
                else {
                    var active =3D document.activeElement;
                    if ((typeof(active) !=3D "undefined") && (active =
!=3D null)) {
                        if ((typeof(active.id) !=3D "undefined") && =
(active.id !=3D null) && (active.id.length > 0)) {
                            lastFocus.value =3D active.id;
                        }
                        else if (typeof(active.name) !=3D "undefined") {
                            lastFocus.value =3D active.name;
                        }
                    }
                }
            }
        }
    }
    if (options.clientSubmit) {
        __doPostBack(options.eventTarget, options.eventArgument);
    }
}
var __pendingCallbacks =3D new Array();
var __synchronousCallBackIndex =3D -1;
function WebForm_DoCallback(eventTarget, eventArgument, eventCallback, =
context, errorCallback, useAsync) {
    var postData =3D __theFormPostData +
                "__CALLBACKID=3D" + WebForm_EncodeCallback(eventTarget) =
+
                "&__CALLBACKPARAM=3D" + =
WebForm_EncodeCallback(eventArgument);
    if (theForm["__EVENTVALIDATION"]) {
        postData +=3D "&__EVENTVALIDATION=3D" + =
WebForm_EncodeCallback(theForm["__EVENTVALIDATION"].value);
    }
    var xmlRequest,e;
    try {
        xmlRequest =3D new XMLHttpRequest();
    }
    catch(e) {
        try {
            xmlRequest =3D new ActiveXObject("Microsoft.XMLHTTP");
        }
        catch(e) {
        }
    }
    var setRequestHeaderMethodExists =3D true;
    try {
        setRequestHeaderMethodExists =3D (xmlRequest && =
xmlRequest.setRequestHeader);
    }
    catch(e) {}
    var callback =3D new Object();
    callback.eventCallback =3D eventCallback;
    callback.context =3D context;
    callback.errorCallback =3D errorCallback;
    callback.async =3D useAsync;
    var callbackIndex =3D =
WebForm_FillFirstAvailableSlot(__pendingCallbacks, callback);
    if (!useAsync) {
        if (__synchronousCallBackIndex !=3D -1) {
            __pendingCallbacks[__synchronousCallBackIndex] =3D null;
        }
        __synchronousCallBackIndex =3D callbackIndex;
    }
    if (setRequestHeaderMethodExists) {
        xmlRequest.onreadystatechange =3D WebForm_CallbackComplete;
        callback.xmlRequest =3D xmlRequest;
        xmlRequest.open("POST", theForm.action, true);
        xmlRequest.setRequestHeader("Content-Type", =
"application/x-www-form-urlencoded");
        xmlRequest.send(postData);
        return;
    }
    callback.xmlRequest =3D new Object();
    var callbackFrameID =3D "__CALLBACKFRAME" + callbackIndex;
    var xmlRequestFrame =3D document.frames[callbackFrameID];
    if (!xmlRequestFrame) {
        xmlRequestFrame =3D document.createElement("IFRAME");
        xmlRequestFrame.width =3D "1";
        xmlRequestFrame.height =3D "1";
        xmlRequestFrame.frameBorder =3D "0";
        xmlRequestFrame.id =3D callbackFrameID;
        xmlRequestFrame.name =3D callbackFrameID;
        xmlRequestFrame.style.position =3D "absolute";
        xmlRequestFrame.style.top =3D "-100px"
        xmlRequestFrame.style.left =3D "-100px";
        try {
            if (callBackFrameUrl) {
                xmlRequestFrame.src =3D callBackFrameUrl;
            }
        }
        catch(e) {}
        document.body.appendChild(xmlRequestFrame);
    }
    var interval =3D window.setInterval(function() {
        xmlRequestFrame =3D document.frames[callbackFrameID];
        if (xmlRequestFrame && xmlRequestFrame.document) {
            window.clearInterval(interval);
            xmlRequestFrame.document.write("");
            xmlRequestFrame.document.close();
            xmlRequestFrame.document.write('<html><body><form =
method=3D"post"><input type=3D"hidden" name=3D"__CALLBACKLOADSCRIPT" =
value=3D"t"></form></body></html>');
            xmlRequestFrame.document.close();
            xmlRequestFrame.document.forms[0].action =3D theForm.action;
            var count =3D __theFormPostCollection.length;
            var element;
            for (var i =3D 0; i < count; i++) {
                element =3D __theFormPostCollection[i];
                if (element) {
                    var fieldElement =3D =
xmlRequestFrame.document.createElement("INPUT");
                    fieldElement.type =3D "hidden";
                    fieldElement.name =3D element.name;
                    fieldElement.value =3D element.value;
                    =
xmlRequestFrame.document.forms[0].appendChild(fieldElement);
                }
            }
            var callbackIdFieldElement =3D =
xmlRequestFrame.document.createElement("INPUT");
            callbackIdFieldElement.type =3D "hidden";
            callbackIdFieldElement.name =3D "__CALLBACKID";
            callbackIdFieldElement.value =3D eventTarget;
            =
xmlRequestFrame.document.forms[0].appendChild(callbackIdFieldElement);
            var callbackParamFieldElement =3D =
xmlRequestFrame.document.createElement("INPUT");
            callbackParamFieldElement.type =3D "hidden";
            callbackParamFieldElement.name =3D "__CALLBACKPARAM";
            callbackParamFieldElement.value =3D eventArgument;
            =
xmlRequestFrame.document.forms[0].appendChild(callbackParamFieldElement);=

            if (theForm["__EVENTVALIDATION"]) {
                var callbackValidationFieldElement =3D =
xmlRequestFrame.document.createElement("INPUT");
                callbackValidationFieldElement.type =3D "hidden";
                callbackValidationFieldElement.name =3D =
"__EVENTVALIDATION";
                callbackValidationFieldElement.value =3D =
theForm["__EVENTVALIDATION"].value;
                =
xmlRequestFrame.document.forms[0].appendChild(callbackValidationFieldElem=
ent);
            }
            var callbackIndexFieldElement =3D =
xmlRequestFrame.document.createElement("INPUT");
            callbackIndexFieldElement.type =3D "hidden";
            callbackIndexFieldElement.name =3D "__CALLBACKINDEX";
            callbackIndexFieldElement.value =3D callbackIndex;
            =
xmlRequestFrame.document.forms[0].appendChild(callbackIndexFieldElement);=

            xmlRequestFrame.document.forms[0].submit();
        }
    }, 10);
}
function WebForm_CallbackComplete() {
    for (i =3D 0; i < __pendingCallbacks.length; i++) {
        callbackObject =3D __pendingCallbacks[i];
        if (callbackObject && callbackObject.xmlRequest && =
(callbackObject.xmlRequest.readyState =3D=3D 4)) {
            WebForm_ExecuteCallback(callbackObject);
            if (!__pendingCallbacks[i].async) {
                __synchronousCallBackIndex =3D -1;
            }
            __pendingCallbacks[i] =3D null;
            var callbackFrameID =3D "__CALLBACKFRAME" + i;
            var xmlRequestFrame =3D =
document.getElementById(callbackFrameID);
            if (xmlRequestFrame) {
                xmlRequestFrame.parentNode.removeChild(xmlRequestFrame);
            }
        }
    }
}
function WebForm_ExecuteCallback(callbackObject) {
    var response =3D callbackObject.xmlRequest.responseText;
    if (response.charAt(0) =3D=3D "s") {
        if ((typeof(callbackObject.eventCallback) !=3D "undefined") && =
(callbackObject.eventCallback !=3D null)) {
            callbackObject.eventCallback(response.substring(1), =
callbackObject.context);
        }
    }
    else if (response.charAt(0) =3D=3D "e") {
        if ((typeof(callbackObject.errorCallback) !=3D "undefined") && =
(callbackObject.errorCallback !=3D null)) {
            callbackObject.errorCallback(response.substring(1), =
callbackObject.context);
        }
    }
    else {
        var separatorIndex =3D response.indexOf("|");
        if (separatorIndex !=3D -1) {
            var validationFieldLength =3D parseInt(response.substring(0, =
separatorIndex));
            if (!isNaN(validationFieldLength)) {
                var validationField =3D =
response.substring(separatorIndex + 1, separatorIndex + =
validationFieldLength + 1);
                if (validationField !=3D "") {
                    var validationFieldElement =3D =
theForm["__EVENTVALIDATION"];
                    if (!validationFieldElement) {
                        validationFieldElement =3D =
document.createElement("INPUT");
                        validationFieldElement.type =3D "hidden";
                        validationFieldElement.name =3D =
"__EVENTVALIDATION";
                        theForm.appendChild(validationFieldElement);
                    }
                    validationFieldElement.value =3D validationField;
                }
                if ((typeof(callbackObject.eventCallback) !=3D =
"undefined") && (callbackObject.eventCallback !=3D null)) {
                    =
callbackObject.eventCallback(response.substring(separatorIndex + =
validationFieldLength + 1), callbackObject.context);
                }
            }
        }
    }
}
function WebForm_FillFirstAvailableSlot(array, element) {
    var i;
    for (i =3D 0; i < array.length; i++) {
        if (!array[i]) break;
    }
    array[i] =3D element;
    return i;
}
var __nonMSDOMBrowser =3D =
(window.navigator.appName.toLowerCase().indexOf('explorer') =3D=3D -1);
var __theFormPostData =3D "";
var __theFormPostCollection =3D new Array();
function WebForm_InitCallback() {
    var count =3D theForm.elements.length;
    var element;
    for (var i =3D 0; i < count; i++) {
        element =3D theForm.elements[i];
        var tagName =3D element.tagName.toLowerCase();
        if (tagName =3D=3D "input") {
            var type =3D element.type;
            if ((type =3D=3D "text" || type =3D=3D "hidden" || type =
=3D=3D "password" ||
                ((type =3D=3D "checkbox" || type =3D=3D "radio") && =
element.checked)) &&
                (element.id !=3D "__EVENTVALIDATION")) {
                WebForm_InitCallbackAddField(element.name, =
element.value);
            }
        }
        else if (tagName =3D=3D "select") {
            var selectCount =3D element.options.length;
            for (var j =3D 0; j < selectCount; j++) {
                var selectChild =3D element.options[j];
                if (selectChild.selected =3D=3D true) {
                    WebForm_InitCallbackAddField(element.name, =
element.value);
                }
            }
        }
        else if (tagName =3D=3D "textarea") {
            WebForm_InitCallbackAddField(element.name, element.value);
        }
    }
}
function WebForm_InitCallbackAddField(name, value) {
    var nameValue =3D new Object();
    nameValue.name =3D name;
    nameValue.value =3D value;
    __theFormPostCollection[__theFormPostCollection.length] =3D =
nameValue;
    __theFormPostData +=3D name + "=3D" + WebForm_EncodeCallback(value) =
+ "&";
}
function WebForm_EncodeCallback(parameter) {
    if (encodeURIComponent) {
        return encodeURIComponent(parameter);
    }
    else {
        return escape(parameter);
    }
}
var __disabledControlArray =3D new Array();
function WebForm_ReEnableControls() {
    if (typeof(__enabledControlArray) =3D=3D 'undefined') {
        return false;
    }
    var disabledIndex =3D 0;
    for (var i =3D 0; i < __enabledControlArray.length; i++) {
        var c;
        if (__nonMSDOMBrowser) {
            c =3D document.getElementById(__enabledControlArray[i]);
        }
        else {
            c =3D document.all[__enabledControlArray[i]];
        }
        if ((typeof(c) !=3D "undefined") && (c !=3D null) && (c.disabled =
=3D=3D true)) {
            c.disabled =3D false;
            __disabledControlArray[disabledIndex++] =3D c;
        }
    }
    setTimeout("WebForm_ReDisableControls()", 0);
    return true;
}
function WebForm_ReDisableControls() {
    for (var i =3D 0; i < __disabledControlArray.length; i++) {
        __disabledControlArray[i].disabled =3D true;
    }
}
function WebForm_FireDefaultButton(event, target) {
        if (event.keyCode =3D=3D 13 && !(event.srcElement && =
(event.srcElement.tagName.toLowerCase() =3D=3D "textarea"))) {
        var defaultButton;
        if (__nonMSDOMBrowser) {
            defaultButton =3D document.getElementById(target);
        }
        else {
            defaultButton =3D document.all[target];
        }
        if (defaultButton && typeof(defaultButton.click) !=3D =
"undefined") {
            defaultButton.click();
            event.cancelBubble =3D true;
            if (event.stopPropagation) event.stopPropagation();
            return false;
        }
    }
    return true;
}
function WebForm_GetScrollX() {
    if (__nonMSDOMBrowser) {
        return window.pageXOffset;
    }
    else {
        if (document.documentElement && =
document.documentElement.scrollLeft) {
            return document.documentElement.scrollLeft;
        }
        else if (document.body) {
            return document.body.scrollLeft;
        }
    }
    return 0;
}
function WebForm_GetScrollY() {
    if (__nonMSDOMBrowser) {
        return window.pageYOffset;
    }
    else {
        if (document.documentElement && =
document.documentElement.scrollTop) {
            return document.documentElement.scrollTop;
        }
        else if (document.body) {
            return document.body.scrollTop;
        }
    }
    return 0;
}
function WebForm_SaveScrollPositionSubmit() {
    if (__nonMSDOMBrowser) {
        theForm.elements['__SCROLLPOSITIONY'].value =3D =
window.pageYOffset;
        theForm.elements['__SCROLLPOSITIONX'].value =3D =
window.pageXOffset;
    }
    else {
        theForm.__SCROLLPOSITIONX.value =3D WebForm_GetScrollX();
        theForm.__SCROLLPOSITIONY.value =3D WebForm_GetScrollY();
    }
    if ((typeof(this.oldSubmit) !=3D "undefined") && (this.oldSubmit =
!=3D null)) {
        return this.oldSubmit();
    }
    return true;
}
function WebForm_SaveScrollPositionOnSubmit() {
    theForm.__SCROLLPOSITIONX.value =3D WebForm_GetScrollX();
    theForm.__SCROLLPOSITIONY.value =3D WebForm_GetScrollY();
    if ((typeof(this.oldOnSubmit) !=3D "undefined") && (this.oldOnSubmit =
!=3D null)) {
        return this.oldOnSubmit();
    }
    return true;
}
function WebForm_RestoreScrollPosition() {
    if (__nonMSDOMBrowser) {
        window.scrollTo(theForm.elements['__SCROLLPOSITIONX'].value, =
theForm.elements['__SCROLLPOSITIONY'].value);
    }
    else {
        window.scrollTo(theForm.__SCROLLPOSITIONX.value, =
theForm.__SCROLLPOSITIONY.value);
    }
    if ((typeof(theForm.oldOnLoad) !=3D "undefined") && =
(theForm.oldOnLoad !=3D null)) {
        return theForm.oldOnLoad();
    }
    return true;
}
function WebForm_TextBoxKeyHandler(event) {
    if (event.keyCode =3D=3D 13) {
        var target;
        if (__nonMSDOMBrowser) {
            target =3D event.target;
        }
        else {
            target =3D event.srcElement;
        }
        if ((typeof(target) !=3D "undefined") && (target !=3D null)) {
            if (typeof(target.onchange) !=3D "undefined") {
                target.onchange();
                event.cancelBubble =3D true;
                if (event.stopPropagation) event.stopPropagation();
                return false;
            }
        }
    }
    return true;
}
function WebForm_AppendToClassName(element, className) {
    var current =3D element.className;
    if (current) {
        if (current.charAt(current.length - 1) !=3D ' ') {
            current +=3D ' ';
        }
        current +=3D className;
    }
    else {
        current =3D className;
    }
    element.className =3D current;
}
function WebForm_RemoveClassName(element, className) {
    var current =3D element.className;
    if (current) {
        if (current.substring(current.length - className.length - 1, =
current.length) =3D=3D ' ' + className) {
            element.className =3D current.substring(0, current.length - =
className.length - 1);
            return;
        }
        if (current =3D=3D className) {
            element.className =3D "";
            return;
        }
        var index =3D current.indexOf(' ' + className + ' ');
        if (index !=3D -1) {
            element.className =3D current.substring(0, index) + =
current.substring(index + className.length + 2, current.length);
            return;
        }
        if (current.substring(0, className.length) =3D=3D className + ' =
') {
            element.className =3D current.substring(className.length + =
1, current.length);
        }
    }
}
function WebForm_GetElementById(elementId) {
    if (document.getElementById) {
        return document.getElementById(elementId);
    }
    else if (document.all) {
        return document.all[elementId];
    }
    else return null;
}
function WebForm_GetElementByTagName(element, tagName) {
    var elements =3D WebForm_GetElementsByTagName(element, tagName);
    if (elements && elements.length > 0) {
        return elements[0];
    }
    else return null;
}
function WebForm_GetElementsByTagName(element, tagName) {
    if (element && tagName) {
        if (element.getElementsByTagName) {
            return element.getElementsByTagName(tagName);
        }
        if (element.all && element.all.tags) {
            return element.all.tags(tagName);
        }
    }
    return null;
}
function WebForm_GetElementDir(element) {
    if (element) {
        if (element.dir) {
            return element.dir;
        }
        return WebForm_GetElementDir(element.parentNode);
    }
    return "ltr";
}
function WebForm_GetElementPosition(element) {
    var result =3D new Object();
    result.x =3D 0;
    result.y =3D 0;
    result.width =3D 0;
    result.height =3D 0;
    if (element.offsetParent) {
        result.x =3D element.offsetLeft;
        result.y =3D element.offsetTop;
        var parent =3D element.offsetParent;
        while (parent) {
            result.x +=3D parent.offsetLeft;
            result.y +=3D parent.offsetTop;
            var parentTagName =3D parent.tagName.toLowerCase();
            if (parentTagName !=3D "table" &&
                parentTagName !=3D "body" &&=20
                parentTagName !=3D "html" &&=20
                parentTagName !=3D "div" &&=20
                parent.clientTop &&=20
                parent.clientLeft) {
                result.x +=3D parent.clientLeft;
                result.y +=3D parent.clientTop;
            }
            parent =3D parent.offsetParent;
        }
    }
    else if (element.left && element.top) {
        result.x =3D element.left;
        result.y =3D element.top;
    }
    else {
        if (element.x) {
            result.x =3D element.x;
        }
        if (element.y) {
            result.y =3D element.y;
        }
    }
    if (element.offsetWidth && element.offsetHeight) {
        result.width =3D element.offsetWidth;
        result.height =3D element.offsetHeight;
    }
    else if (element.style && element.style.pixelWidth && =
element.style.pixelHeight) {
        result.width =3D element.style.pixelWidth;
        result.height =3D element.style.pixelHeight;
    }
    return result;
}
function WebForm_GetParentByTagName(element, tagName) {
    var parent =3D element.parentNode;
    var upperTagName =3D tagName.toUpperCase();
    while (parent && (parent.tagName.toUpperCase() !=3D upperTagName)) {
        parent =3D parent.parentNode ? parent.parentNode : =
parent.parentElement;
    }
    return parent;
}
function WebForm_SetElementHeight(element, height) {
    if (element && element.style) {
        element.style.height =3D height + "px";
    }
}
function WebForm_SetElementWidth(element, width) {
    if (element && element.style) {
        element.style.width =3D width + "px";
    }
}
function WebForm_SetElementX(element, x) {
    if (element && element.style) {
        element.style.left =3D x + "px";
    }
}
function WebForm_SetElementY(element, y) {
    if (element && element.style) {
        element.style.top =3D y + "px";
    }
}

------=_NextPart_000_0000_01CA4DB9.92AFE550--
