import pandas as pd
from datetime import datetime

# 读取完整的术语数据
df = pd.read_csv('complete_terminology.csv')

# 创建标准对照数据
standards_data = {
    '标准代码': ['USCAR-24-2', 'AKLV-03', 'CFR-49 Section 178.65', 'SAE J403', 'AWS标准', 'ASTM标准', 'ASME标准', 'OSHA标准', 'ISO标准', 'DOT法规'],
    '标准名称': ['汽车气囊发生器标准', '欧洲汽车安全标准', 'DOT 39压力容器规范', '钢材化学成分标准', '焊接工艺标准', '材料测试标准', '机械工程标准', '职业安全健康标准', '国际标准化组织标准', '危险品运输法规'],
    '发布机构': ['USCAR', '欧盟', '美国运输部', 'SAE', '美国焊接学会', 'ASTM', 'ASME', 'OSHA', 'ISO', 'DOT'],
    '适用范围': ['气囊发生器设计、测试、认证', '欧洲市场气囊发生器要求', '压力容器设计制造', '汽车用钢材规范', '焊接工艺和质量', '材料性能测试', '压力容器和检测', '化学品安全管理', '质量管理体系', '危险品运输安全'],
    '对应HY文档': ['多个HY文档', '质量控制文档', 'CFR-49文件', 'HY03058', 'HY03051等', '材料规范文档', 'HY03051', 'MSDS文件', '质量管理文档', 'MSDS文件'],
    '备注': ['主要参考标准', '欧洲认证标准', '压力容器法规', '材料标准', '焊接技术标准', '材料测试标准', '机械工程标准', '安全管理标准', '质量管理标准', '运输安全法规']
}
standards_df = pd.DataFrame(standards_data)

# 创建产品系列数据
product_series_data = {
    '产品系列': ['CADH', 'PH7', 'AHS', 'G2P', 'G3P', 'SH5', 'CH5', 'PH6', 'APH'],
    '英文代码': ['CADH Series', 'PH7 Series', 'AHS Series', 'G2P Series', 'G3P Series', 'SH5 Series', 'CH5 Series', 'PH6 Series', 'APH Series'],
    '中文名称': ['圆柱形驾驶员侧发生器', '饼状发生器第7代', '混合式发生器', '新一代发生器', '第三代新发生器', '侧气囊发生器', '帘式气囊发生器', '饼状发生器第6代', '亚太地区专用'],
    '产品类型': ['驾驶员侧', '乘客侧', '多用途', '新技术', '新技术', '侧气囊', '帘式气囊', '乘客侧', '区域定制'],
    '主要特征': ['圆柱形结构，双级传爆', '饼状结构，大容量', '混合气体，可调节', '球形电阻焊，高效率', '进一步优化设计', '小型化设计', '长条形结构', '前代产品', '中国市场专用'],
    '相关HY文档': ['HY03065, HY03075', 'HY03183, HY03184', 'HY03051, HY03044', 'HY03232', 'HY03252', 'HY03051, HY03044', 'HY03051, HY03044', 'HY03175, HY03177', 'HY03163, HY03165'],
    '状态': ['生产中', '生产中', '生产中', '开发中', '规划中', '生产中', '生产中', '停产', '生产中']
}
product_df = pd.DataFrame(product_series_data)

# 创建Excel文件
with pd.ExcelWriter('汽车安全气囊发生器专业术语库_完整版.xlsx', engine='openpyxl') as writer:
    
    # 1. 使用说明工作表
    instruction_data = {
        '项目': ['文档名称', '版本号', '创建日期', '基于标准', '数据来源', '术语总数', '技术领域数', '产品系列数'],
        '内容': ['汽车安全气囊发生器专业术语库', 'V1.0', datetime.now().strftime("%Y-%m-%d"), 'USCAR-24-2, AKLV-03, CFR-49 Section 178.65', 'HY Files技术规范文档库', len(df), len(df['技术领域'].unique()), len(df['产品系列'].unique())]
    }
    instruction_df = pd.DataFrame(instruction_data)
    instruction_df.to_excel(writer, sheet_name='📋 使用说明', index=False)
    
    # 2. 术语总览
    df.to_excel(writer, sheet_name='🔍 术语总览', index=False)
    
    # 3. 按技术领域分类
    domains = df['技术领域'].unique()
    for domain in domains:
        domain_df = df[df['技术领域'] == domain]
        sheet_name = domain.replace('与', '&')[:10]  # 简化工作表名称
        domain_df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    # 4. 按术语等级分类
    levels = df['术语等级'].unique()
    for level in levels:
        level_df = df[df['术语等级'] == level]
        sheet_name = f"等级{level}"
        level_df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    # 5. 标准对照表
    standards_df.to_excel(writer, sheet_name='📚 标准对照表', index=False)
    
    # 6. 产品系列码
    product_df.to_excel(writer, sheet_name='🏷️ 产品系列码', index=False)
    
    # 7. 术语索引（按英文首字母）
    index_data = []
    for idx, row in df.iterrows():
        first_letter = row['英文术语'][0].upper() if row['英文术语'] else 'Z'
        index_data.append([first_letter, row['中文术语'], row['英文术语'], idx+2, row['技术领域']])
    
    index_df = pd.DataFrame(index_data, columns=['索引字母', '中文术语', '英文术语', '所在行号', '技术领域'])
    index_df = index_df.sort_values('索引字母')
    index_df.to_excel(writer, sheet_name='🔎 术语索引', index=False)
    
    # 8. 统计分析
    stats_data = []
    stats_data.append(['基本统计', '', ''])
    stats_data.append(['术语总数', len(df), ''])
    stats_data.append(['', '', ''])
    stats_data.append(['按技术领域分布', '', ''])
    
    domain_stats = df['技术领域'].value_counts()
    for domain, count in domain_stats.items():
        stats_data.append([domain, count, f"{count/len(df)*100:.1f}%"])
    
    stats_data.append(['', '', ''])
    stats_data.append(['按术语等级分布', '', ''])
    
    level_stats = df['术语等级'].value_counts()
    for level, count in level_stats.items():
        stats_data.append([level, count, f"{count/len(df)*100:.1f}%"])
    
    stats_df = pd.DataFrame(stats_data, columns=['统计项目', '数量', '百分比'])
    stats_df.to_excel(writer, sheet_name='📊 统计分析', index=False)

print("完整版Excel文档创建完成：汽车安全气囊发生器专业术语库_完整版.xlsx")
print(f"包含 {len(df)} 个术语，{len(df['技术领域'].unique())} 个技术领域")
