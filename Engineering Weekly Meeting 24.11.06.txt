# Engineering Weekly Meeting

  Meeting started: 2024/11/6 09:09:44
  Duration: 50 minutes
  Participants: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>

  [View original transcript](https://app.tactiq.io/api/2/u/m/r/5rDGejuMNEkmTt0wFaMr?o=txt)

  

  
  
  
    ## Highlights

> 02:29 <PERSON>: In my opinion, the internal pressure is insufficient for the deployment test. We aim to compare the sleeve hydroburst test with the current condition. If there are differences, we might identify them, but it could be challenging to detect any issues during the folder deployment test.
> 10:24 <PERSON>: Yes, you can observe that. Yes, 123. The last booster will undergo environmental testing for each loss, and we need to dispatch the environmental test. We've only experienced one loss, the first booster, which I believe is not a significant issue. The second and third losses are concerning.
> 11:05 <PERSON>: Additionally, here are the sorted weights of the boosters and the guest weights from three lots. The first lot includes 30 pieces for loss one booster, and the second lot also has 30 for loss two. From this data, there is almost no significant difference. The guest weight for loss one booster may be greater than the others, but the test results are better. Overall, I believe there are no major issues since they all meet the required specifications.
> 16:33 <PERSON>: Regarding the casino hotel temperature, I think the performance is satisfactory, but the ambient and hot conditions are not ideal. In summary, all the coded data appears acceptable.
> 23:23 <PERSON>, <PERSON>bekah: Perhaps we can check tomorrow.
> 25:21 <PERSON>: Yes, I think this is more crucial than the booster weight.
> 27:22 <PERSON> <PERSON>: You should obtain that from us and send it to ssow. We are already aware of this program. I haven't reviewed it yet, but I plan to do so either today or tomorrow.
> 29:21 Super <PERSON>: Also, <PERSON> is inquiring about the tech creation between and scopia. Did you complete the report? I believe we can discuss it tonight.
> 33:47 Jim Liu: I think I might have learned something from another competitor or OEM. As we understand, it may have been developed by dog. So, generally, why does F want a Fisher or standard checklist for anything?
> 36:43 Benthall, Sam E: I will see what we currently have to address that specific question, but we will have another DV completed, and our goal is also to finish the QFS portion with this TV.
> 38:28 Jim Liu: Yes, the sixth piece is single. They conducted one module test, and the ambient test results were reported by their engineering team as not being a significant issue. They will proceed with more tests, but they need to reinforce their test fixture.
> 41:26 Jim Liu: Alright. Perhaps you can check my email tomorrow and provide me with some feedback.
> 48:58 Kevin Lu: Oh, okay. So maybe you can check and send me an email tomorrow.
> 49:02 Benthall, Sam E: I probably won't be able to check tomorrow, but if you send me an email, I can look into it eventually.
> 49:12 Benthall, Sam E: I will not be working tomorrow.
> 49:27 Benthall, Sam E: I will check, but I cannot do it tomorrow.

## Transcript

00:00 Jim Liu: If there are no questions regarding any errors, let's proceed to synchron inflators and discuss product applications and the program. Barry and Lukie, do you have any additional points?
00:27 Lukie Lu: I'm fine.
00:30 Barry Wang: I have some points. I copied the first one I want to discuss regarding the washer parts test matrix. I noticed feedback from Scott, who expressed concern that if we test the components manifold, it may differ from our production parts. He suggested we conduct the deployment tests at high temperatures, but from my perspective...
01:16 Colburn, Scott: Hey, Barry, I was just concerned about your note indicating that you would perform your hydroburst tests with conformance parts.
01:20 Barry Wang: Yes.
01:28 Colburn, Scott: Are those conformance parts identical? Are they stamped parts and do they have the same wash material? If they don't, that's my concern. We need to ensure the parts we use for testing have the same properties and are affected by the wash in the same manner.
01:57 Barry Wang: Yes, you're right. We share this concern. However, for the hybrid test, using the normal manifold may complicate the testing process.
02:12 Colburn, Scott: Understood. That's why I suggested we conduct tests at high temperatures or even around 100°.
02:20 Colburn, Scott: I don't know, something to apply extra pressure.
02:29 Barry Wang: For the deployment test, I believe the internal pressure is inadequate. We want to compare the sleeve hydroburst test with the current status. If there are any discrepancies, we may identify them, but it could be challenging to detect issues during the folder deployment test.
03:04 Colburn, Scott: Understood. I was just trying to emphasize the need for testing parts that will be used in production, not just hydroburst parts.
03:19 Jim Liu: Hmm. Okay, I understand. For the hydroburst sleeve hydroburst sample, we will use a manifold without hover, but with a new version process. This is one point. The second point is that I understand your concern, Scott, regarding the use of the manifold with the HO.
04:03 Jim Liu: For the mass production parts, I believe we can include some high-temperature tests as an additional requirement without eliminating the original hydroburst machine.
04:19 Colburn, Scott: Right, right, right. I wasn't suggesting we remove anything; I was saying we need to add something to ensure the actual parts we're going to produce in the plant are tested as well.
04:23 Jim Liu: Yes, yes, yes, I think this is acceptable.
04:34 Barry Wang: Okay.
04:38 Jim Liu: This is good because, as I discussed with Barry the day before yesterday, we need to evaluate this new process carefully. If anyone can add some dual care tests to the test matrix, that would be very beneficial.
04:58 Jim Liu: So Scott, I believe it's acceptable to include this additional testing requirement. It's very helpful.
05:09 Barry Wang: Okay, with deployment at 100°.
05:15 Jim Liu: Yes, perhaps 10 pieces, Scott?
05:20 Colburn, Scott: Yes, that would be fine. I was just trying to ensure we have something with the actual manifolds and igniter body.
05:33 Jim Liu: Yes, and for us, 30 thousand, perhaps 10 pieces for each lot?
05:42 Colburn, Scott: Well, I was thinking, aren't you going to implement some kind of step-down process anyway?
05:43 Jim Liu: Each lot. Each lot.
05:49 Colburn, Scott: With your inspections, you may not need to go all the way out to 10, but that's fine too.
05:59 Jim Liu: Okay. Yes, understood. Maybe not ten pieces. Barry, after the meeting, you can evaluate the test, but ten pieces should suffice for you, Scott, am I correct?
06:23 Colburn, Scott: Yes, that would be plenty, in my opinion. Of course, Rebecca, I don't know if Brian ever called in. Let me know if you need more.
06:39 Jim Liu: That's good. How about you, Rebecca? What’s your opinion?
06:44 Reid, Rebekah: I think I'm fine.
06:49 Jim Liu: Okay, okay, okay. After the meeting, Barry will send the test matrix to the plant. If we are in agreement, after the meeting and some modifications by Barry, he will send the test matrix to Thomas and Reggie. Sounds good? Can we conclude this topic? Thank you.
07:28 Barry Wang: Okay. Another question is that I want to review the AM baseline data for QFS. It appears that the three lots of data show considerable variation, particularly at hot and ambient temperatures. If we apply the US CUR limits, it may be challenging to meet these specifications. The P-15 here indicates that the first lot of the IT boosts seems better. You can observe the high temperature, ambient temperature, and cold temperature data, but the three boosters show some variation, especially in the ambient conditions.
08:34 Barry Wang: Uh, the code is best.
08:41 Barry Wang: This is the last three booster parts' ambient and cold data. I have summarized all the data in this table.
09:00 Barry Wang: Currently, we have completed their AM building, so we await our decision on whether we can proceed with environmental testing for both parts.
09:17 Reid, Rebekah: Sir, you mentioned that the majority of the environmental tests came from lot 1, right?
09:21 Barry Wang: I thought so, based on the test matrix. I believe they also include loss two and loss three. I can refer to the test matrix.
09:41 Jim Liu: Barry, do we have enough samples for each lot?
09:50 Barry Wang: Yes, I believe we have sufficient samples.
09:55 Jim Liu: Okay.
09:56 Barry Wang: Each lot has spare parts, but I'm uncertain about the baseline data as it doesn't seem good to me. I believe if both are the same, it results in a loss. If you ever want to test, the available test results aren't satisfactory, yes.  
10:17 Reid, Rebekah: So LOT 3 was group E. Was that the only one that went through? What is group E? Quintill.  
10:24 Barry Wang: Yes, you can observe it. Yes, 123. Yes, the last booster will undergo environmental testing, so we need to ship out each loss for the environmental test. We have only lost one booster, the first one. I think it's not a significant issue. The second and third losses are where the issue lies.  
11:05 Barry Wang: Additionally, I have some sorted data here regarding the booster weight and guest weight from three lots. The first 30 pieces correspond to the loss of one booster, and then the second is 30, which is loss 2. However, from this data, there is almost no significant difference. As for loss one booster, the guest weight might be greater than the others, but the test results are better. From this data, I think there’s no major issue as they all meet the hours specification.  
12:00 Reid, Rebekah: Do you recall the BPFs for each lot? 2:00 and 3:00.  
12:09 Barry Wang: I think I have the data; I need to retrieve this. Please hold on.  
12:20 Reid, Rebekah: Is there an option to rebuild Lot 3?  
12:24 Barry Wang: I believe I remember that the first loss had the highest BPF compared to the other two. Please wait a moment.  
12:44 Reid, Rebekah: Hey, Scott, when you conduct car dates, do you recall if you need to combine all three lots for the baseline?  
12:50 Colburn, Scott: I would need to check how it's documented, and I’m not sure what it states in version 3.  
12:51 Reid, Rebekah: So, would you...?  
![Screenshot](undefined)  
12:57 Reid, Rebekah: Yes, I was trying to remember if the gates need to be around just lot 1, you know, for the ones that go to lot 1, or if they need to encompass all three.  
![Screenshot](undefined)  
13:11 Colburn, Scott: Just ensure that your Group A is supposed to include parts from all three.  
13:17 Barry Wang: So it’s...  
13:19 Reid, Rebekah: And then any sequentials or environments you conduct afterward must meet the baseline.  
13:25 Colburn, Scott: They must fall within the gates established by the baseline unless that has changed, but I doubt that part would have changed.  
13:36 Barry Wang: Let’s see the BPF information here. The first of the losses had the highest BPF.  
![Screenshot](undefined)  
13:48 Barry Wang: Perhaps this is the reason.  
13:53 Reid, Rebekah: So the options are a lot more. We're not particularly concerned about.  
13:59 Barry Wang: Yes, I can obtain it.  
13:59 Reid, Rebekah: Who's a bit more variable, right?  
![Screenshot](undefined)  
14:01 Barry Wang: Now, yes.  
14:04 Reid, Rebekah: Then LOT 3 is not very good at all.  
14:08 Barry Wang: Yes, the first and last name are the best you can see. The second and third are not very good. So the code temperature for all of them is in the same batch. Let’s see here. Intention. So perhaps we can only sift a lost one to conduct tests. I’m not sure.  
14:51 Reid, Rebekah: We probably don’t have enough from lot 1 to cover all the other tests.  
14:59 Barry Wang: Yes. This is the quality for this. For the first loss, we have more parts, but they require additional tests for the first loss percentage.  
15:29 Reid, Rebekah: You have extras, right? You mentioned having 15.  
15:38 Barry Wang: Oh yes. We have... wait... 30. Maybe 30 or 20, probably.  
15:47 Reid, Rebekah: No fruit-based vines, okay.  
15:47 Barry Wang: Yes, yes, yes. Yes, please remember to test each loss for the baseline. We have 10 or 15 spare parts. Perhaps we can test all the spare parts.  
16:26 Reid, Rebekah: The cold tests appear pretty good. So you really need to test a bit more under hot and ambient conditions.  
16:33 Barry Wang: In the casino, hotel temperature. I believe the performance is good, but for ambient and hot, it’s not satisfactory. I think summarising all the cold data should be okay.  
16:39 Reid, Rebekah: Yes.  
17:15 Barry Wang: What’s your opinion on this for their environmental test?  
17:33 Reid, Rebekah: I know I’m thinking we may have to test extra baselines and see if you can improve the results. If you can’t, then we might have to rebuild. If you can’t enhance the baselines, there’s no way to improve the environments regardless of how much you test.  
17:55 Colburn, Scott: What if they do as you suggested and rebuild group three with a different lot of grain?  
18:04 Reid, Rebekah: I suppose it depends on whether they...  
18:04 Colburn, Scott: That gets added into the baselines, right?  
18:04 Reid, Rebekah: I guess it depends. If they had it, it would just replace group three, yes. But I’m uncertain. It seems like group 22 would also require some extra tests, right?  
18:25 Barry Wang: Group two, I think, has poor performance at ambient temperature.  
18:42 Reid, Rebekah: How many are hanging out like 3? Is it about 3 curves that are somewhat isolated?  
18:47 Barry Wang: Sorry.  
18:50 Reid, Rebekah: I can’t tell.  
19:02 Barry Wang: We can test them all to see what we need to do again.  
19:07 Reid, Rebekah: Yes, I think we should test a bit more for hot and ambient conditions for lot 2 and lot 3 and see what we find just for baselines. If it looks a bit better, then perhaps we could proceed to send those lots out, but I’m not sure if it will. Is there another lot of propellant? If we had to rebuild, do you have something closer to lot 1?  
19:34 Barry Wang: I think we might have more lots of boosters because the SH5 and SH 5.1 B are in production.  
19:47 Reid, Rebekah: Is it...? I can’t remember. I was about to say sorry.  
19:53 Barry Wang: Okay, this data only has 10 pieces for each loss for each booster.  
20:00 Reid, Rebekah: Yes.  
20:00 Barry Wang: Yes, because the new test matrix requires more tests, but our plant is waiting for the pack wash together with every T, so other parts aren’t tested at all.  
20:18 Reid, Rebekah: Okay. So it might appear even worse when you do the tank wash. So it might look even worse. That’s what we usually observe, right?  
20:28 Barry Wang: Okay.  
20:30 Reid, Rebekah: Scott, you get a bit more variability with the tank wash parts.  
20:35 Colburn, Scott: Do we really?  
20:38 Reid, Rebekah: Oh no.  
20:40 Colburn, Scott: I shouldn’t behave tank-wise the same. Now you’re going to have variation in your tank content. But I don’t think they add to any variation in the actual tanks themselves. If they do, then you’re not cleaning your tank between the other firings.  
21:08 Reid, Rebekah: I suggest testing a few more, Barry if possible. Maybe do 3 hot and three ambient. I can’t tell if that would be enough.  
21:17 Barry Wang: Okay.  
21:19 Reid, Rebekah: If it helps, just for lot 2 and 3, I think lot 1 appeared quite good.  
21:27 Barry Wang: Okay, okay. That’s good.  
21:29 Reid, Rebekah: Okay. What’s the...  
21:35 Barry Wang: Okay.  
21:36 Reid, Rebekah: What’s the... What’s the spec limit for the BPF on that booster? Is it... is it getting close to the low number?  
21:48 Barry Wang: PPF. Oh, I remember it is...  
21:55 Reid, Rebekah: Do you recall, Dylan?  
21:58 Sanders, Dylan: I thought it was something like 415 ± 30.  
22:09 Reid, Rebekah: It’s around 380. It’s about 385.  
22:13 Barry Wang: For me? I’m not sure if we’re on the latest one, but...  
22:40 Reid, Rebekah: It’s not 15.  
22:43 Barry Wang: Yes.  
22:45 Reid, Rebekah: So it seems like that one lot would be out, right? So it appears that one lot would be out, or two lots are below 400.  
22:58 Barry Wang: Yes, I believe the first one is out of the back a little.  
23:02 Reid, Rebekah: No. And that lot three is out too, right? But perhaps that’s not the newest spec. This is concerning material, right?  
23:16 Barry Wang: Yes, I’m not certain. Sorry, I’m not sure if this is the latest spec on hand.  
23:23 Reid, Rebekah: Maybe we can check tomorrow.  
23:26 Barry Wang: Okay, and I think...  
23:26 Reid, Rebekah: Are you saving this in a folder we can access? Can you save the PV data where we can view it?  
23:37 Barry Wang: Okay.  
23:37 Reid, Rebekah: Like on the arrow drive.  
23:43 Barry Wang: Okay. And I think perhaps for better results, if there are significant variations, if we rebuild the samples, we might be able to increase the booster rate. I think there may be some internal pressure variations. Even if it’s lower, perhaps there are variations that can enhance the boost weight for better results.  
24:18 Reid, Rebekah: Okay. Maybe that’s the backup plan.  
24:21 Jim Liu: The curiosity is the tank performance and cold temperature is stable.  
24:21 Reid, Rebekah: Maybe that’s the backup plan.  
24:28 Barry Wang: Yes.  
24:29 Jim Liu: I mean if the ILP is lower, it’s not sufficient because the booster weight isn’t enough, so the tank performance and current temperature should be more variable due to...  
24:55 Barry Wang: Yes. So another point I think may be relevant is regarding the sleeve open status. Perhaps with lower internal pressure, the sleeve opens sufficiently at the cold temperature.  
25:17 Jim Liu: Yeah.  
25:17 Barry Wang: The other two temperatures.  
25:21 Jim Liu: Yeah, maybe. Yeah, I think this is more crucial than the booster weight.  
25:32 Barry Wang: Okay, and then we can conduct more tests.  
25:38 Jim Liu: Okay.  
25:47 Barry Wang: Okay.  
25:49 Reid, Rebekah: It’s Barry.  
25:49 Barry Wang: This all formats Ian.  
25:55 Jim Liu: So what’s our schedule to send out the parts to start the test lab?  
26:03 Super Guo: As soon as we confirm the performance, if we are okay, we need to send out immediately.  
26:11 Jim Liu: Okay. So if a... so I suggest maybe we should build the third lot in parallel. I mean, we’re okay. We were conducting more tests, and perhaps in parallel we can prepare to build the first lot to replace and need to replace lot 3.  
26:45 Reid, Rebekah: Okay.  
26:48 Jim Liu: Maybe.  
26:56 Barry Wang: Okay, okay. That’s all from my side.  
27:08 Super Guo: And then Rebecca, I have one question regarding the KO program and the SSOW. Did you review that?  
27:20 Reid, Rebekah: I think I saw it.  
27:22 Super Guo: You should receive that from us, and he sends it to SSOW. So you know we are already aware of this program. I haven’t reviewed it yet, but I plan to review it today or tomorrow.  
27:36 Super Guo: But you need to please review it as well, and if there are no questions, I think yes, or will sign off the SSOW and then return to the YF.  
27:39 Reid, Rebekah: Okay. Okay, do you think I have it though?  
27:50 Super Guo: You should have it.  
27:51 Reid, Rebekah: Okay, okay. Well, if you’re looking at it and don’t see... well, if you’re looking at it and don’t see my name forwarded, okay.  
27:56 Super Guo: I believe I got your email. I have your name.  
28:00 Reid, Rebekah: Okay.  
28:05 Super Guo: Anyway, if I don’t see you claim, I will support you.  
28:13 Reid, Rebekah: Okay, thanks.  
28:14 Super Guo: That’s for cylindrical. And I have several items for Sam. Yesterday, I sent an email to you and Chris asking for a single timeline and status. And I didn’t see Chris reply. If you see Chris, can you ask him to reply to me?  
28:45 Benthall, Sam E: Okay. Just the general timeline or for...?  
28:48 Super Guo: Yeah, yeah, yeah, yeah.  
28:51 Benthall, Sam E: For like PV build type of thing.  
28:55 Super Guo: Yes, yes, just the general timeline schedule for that.  
29:02 Benthall, Sam E: Okay.  
29:04 Super Guo: I think it should be okay, but I need you or Chris to double confirm.  
29:10 Benthall, Sam E: Okay, I didn’t see the email, but that’s easy to reply to. It’ll be the same thing that I talked about in the off-site meeting.  
29:21 Super Guo: Okay, and one more thing, you know Hubert is asking about the tech creation between and Scopia. Did you complete the report? I believe today, I mean tonight, we can...  
29:22 Benthall, Sam E: I’ll send it to you.  
29:43 Super Guo: Ask for that and also for the 5W data PV report and the final test. I think the final test for 5T needs a written scope here to build a pass.  
29:59 Benthall, Sam E: Yeah, the final test for 5W is in the lab to be tested. The 5T will be done after that, and then we’ll test Scopia parts when we receive them. Scopia Hammering is the PV report for 5W, but I haven’t checked the status.  
30:16 Super Guo: Mm hmm.  
30:24 Benthall, Sam E: Yes. I have not started a report for the tank comparison, but I sent the comparison data.  
30:38 Super Guo: Yeah, yeah, yeah. I think it’s quite easy to complete the paper report. But maybe we can work on it in the meeting today, and afterward, we can send the final report to him.  
30:56 Benthall, Sam E: Yeah, I can do the report. I just can’t do it now.  
30:57 Super Guo: Okay, I understand. I know, I know. And for 5T, if I noticed.
30:59 Benthall, Sam E: Alright.  
31:03 Super Guo: Will you start with the inbox first, and then wait for? Pass and pass again? Or perhaps you could wait for the pass and do both at the same time, I mean.  
31:17 Benthall, Sam E: I was planning to do the 5T sooner so that there would be fewer parts to test when the scopia ones arrive; the same technician will handle all the tests.  
31:32 Super Guo: OK. That makes sense. That's all I have.  
31:44 Jim Liu: Do you have any questions for Sam regarding page 10?  
31:50 Barry Wang: Yes, I have a few questions. I sent an email yesterday because during our meeting, they asked if we completed the checklist for the PH10. Perhaps it relates to the document work, I think. And another question, yes, about the checklists.  
32:19 Jim Liu: OK.  
32:20 Barry Wang: Another question is they want to receive some flying test results. If we, the El Team, have their test results, maybe we can share some information with them.  
32:40 Benthall, Sam E: Which checklist are they asking about?  
32:45 Barry Wang: It's a laugh technique because they have a checklist for their new inflator for their company? I think I sent the checklist before.  
33:00 Benthall, Sam E: Yes, I almost remember it.  
33:03 Barry Wang: Yes. So currently they are asking if we completed a checklist regarding the PH10.  
33:08 Benthall, Sam E: Do they have a program for us?  
33:13 Barry Wang: No, I don't think there is a program, but they want to use their information; first, they will complete the checklist.  
33:25 Benthall, Sam E: I don't think we have had to do that before. Is this new?  
33:29 Barry Wang: Yes, I think we haven't had to do that before.  
33:32 Benthall, Sam E: Yes. Let's see if they have a program for us.  
33:38 Jim Liu: Sammy's next.  
33:39 Benthall, Sam E: We prefer to work with JSS.  
33:47 Jim Liu: Then I think I learned something from another competitor or gained insights from another OEM. As we understand it, maybe it was developed by Dog. OK, but generally, why does F want a Fisher or standard checklist for any?  
34:03 Benthall, Sam E: I don't think so, yes.  
34:11 Jim Liu: New inflator, they want to use in the future. So that's fine. Barry sent this checklist to you in a previous email. You can check if you didn't receive it, and Barry can resend it if necessary.  
34:25 Benthall, Sam E: Yes, I think I remember it.  
34:34 Jim Liu: OK. Please look into the checklist. Let's determine what points we can complete, so they won't ask us to fill it out and provide their feedback item by item. After you check, I think it will be clearer, yes. OK. Another point, Sam? Do we have the flaming test for page 10, single or dual?  
35:25 Benthall, Sam E: Yes, we have some flaming tests.  
35:30 Jim Liu: OK.  
35:31 Benthall, Sam E: The single one doesn't count anymore because there's a new design.  
35:31 Jim Liu: So can you share with? Yes, for sure. OK.  
35:37 Benthall, Sam E: Yes.  
35:40 Jim Liu: So can you share with Barry and me some flaming video or pictures about the dual PH10? Because I remember in our presentation to any customer, we mentioned that the flaming and tag of a PH10 would be better than G2P or something like that, right?  
36:05 Benthall, Sam E: Right, right.  
36:05 Jim Liu: That's what I recall.  
36:07 Benthall, Sam E: Yes, that's correct.  
36:07 Jim Liu: Yes. OK, OK.  
36:11 Benthall, Sam E: Yes. So we are working on a new delta DV.  
36:24 Jim Liu: For dual stage or single stage?  
36:26 Benthall, Sam E: For dual. For dual, yes, our plan is to build the Delta DV in December.  
36:34 Jim Liu: Mm hmm.  
36:37 Benthall, Sam E: So we will have the newest information and results.  
36:38 Jim Liu: OK.  
36:43 Benthall, Sam E: I'll see what we have that we can provide now for that specific question, but we will also have another DV that's completed. Our goal is to finish the QFS portion with this DV as well.  
36:59 Jim Liu: OK. So perhaps you can share with us, just Barry and me, the current flaming test results or pictures.  
37:10 Benthall, Sam E: OK.  
37:11 Jim Liu: I think if we will have delta or delta flaming in December, then Barry and I may not share this information with you guys until we receive the test data in December, and we will share it with you again.  
37:35 Jim Liu: So, OK. That's better. Do we have anything else regarding PH10?  
37:48 Barry Wang: No, I don't have anything. Thanks.  
37:51 Jim Liu: OK. Sam, just before the meeting, I sent you an email about East Ji. East John Long would like to obtain some dual PH10 samples. John Long would like to get five pieces for 2.8 more and five pieces for 3.0 more, but they want these samples for free. Are you okay with this? It's a total of 10 sample pieces.  
38:22 Benthall, Sam E: We haven't sent East Troy Long samples before, have we?  
38:28 Jim Liu: Yes, the sixth piece is single. They did one module test, and the ambient test results were that their engineering team told me there were no significant issues, so they will conduct more tests, but they needed to reinforce their test fixture.  
38:43 Benthall, Sam E: Run.  
38:51 Jim Liu: They modified the test fixture. After that, they conducted a single inflator in a module test. Due to the positive results with the single stage, they would like to evaluate the dual stage.  
39:07 Benthall, Sam E: OK.  
39:07 Jim Liu: Yes.  
39:08 Benthall, Sam E: Yes, I think Bob Knight or someone from marketing needs to confirm the pricing.  
39:16 Jim Liu: OK. That's fine. I will discuss it with Leno and Bob. Generally, if the sales team is okay, we are fine, right?  
39:23 Benthall, Sam E: Yes, I think so. Oh, yes, I believe Bob previously mentioned that the first 12 samples or the first 10 samples would be free, and then there would be a charge after that. But I'm not sure if that's the latest status or pricing.  
39:43 Jim Liu: Understood. The previous support and JSS indicated that 12 pieces can be free, yes. It's also for you guys, yes. OK. If so, I will discuss it with Nano and Bob, as so far we have given East Jungle six pieces and the GSS team eight pieces, yes. OK. So, Sam, can you provide your schedule? Just roughly?  
40:41 Benthall, Sam E: We will need to check the component status.  
40:45 Jim Liu: OK. How about just roughly? In four weeks, three weeks, or four weeks? Is that possible?  
40:53 Benthall, Sam E: Did you say 10 samples each for two different outputs, or five and five?  
40:59 Jim Liu: Five and five, five pieces for each, totaling 10 pieces.  
41:06 Benthall, Sam E: Umm. Probably four weeks, but we need to confirm the component status and the schedule for all the other items first to be sure.  
41:26 Jim Liu: OK. So, maybe tomorrow you can check my email and just give me some rough feedback.  
41:33 Jim Liu: I think it should be fine, yes.  
41:41 Benthall, Sam E: OK.  
41:43 Jim Liu: Yes, I mean after your internal check, I mean. OK. Thanks. So I don't have any other topics. If anyone has any topics.  
42:02 Lukie Lu: Team, I just got.
42:02 Jim Liu: Alright, please go ahead.  
42:06 Lukie Lu: Yes, I have a quick question. I just received a phone call from a customer at Yf. They mentioned that I always share my screen. They are checking the HS 1.5 mole, and I believe this code is DY. They asked me about the specification parameters of this part, and you can see the major diameter, pitch diameter, and minimum diameter, which differ from the. He should join me.  
42:46 Lukie Lu: You can see that they differ from this, and I also checked the start join. The diameter of the start join is also different from this join. This join is our released ECL join. However, the parameters differ from our start join. Is there any SL change for the start, or are we simply replacing it with a new start?  
43:39 Lukie Lu: Scott, Rebecca, do you have any information on this?  
43:45 Colburn, Scott: I haven't been involved with these stud changes and the adjustments to the 6.8 and so on.  
43:46 Reid, Rebekah: Do.  
43:52 Colburn, Scott: Dylan, do you know?  
43:53 Reid, Rebekah: Dylan's been handling it, yes.  
43:53 Colburn, Scott: Dylan, do you have any insights?  
43:59 Barry Wang: Look here, I think there's not a drawing.  
43:59 Lukie Lu: I believe there's not the latest one.  
44:00 Sanders, Dylan: Sorry, what was the question again?  
44:02 Barry Wang: It's not the latest one.  
44:07 Lukie Lu: Are you saying this join is not the latest version?  
44:07 Barry Wang: I mean, these documents now don't want to send me here. Yes.  
44:14 Lukie Lu: It's.  
44:16 Jim Liu: Okay, I noticed that the start name is M6 by 1.0 dash 6H, but on the right drawings, dash 66, I think it's different, right?  
44:32 Reid, Rebekah: It relates to the coding, Jim.  
44:36 Jim Liu: Mm hmm.  
44:37 Reid, Rebekah: The studs start out as G threads, but when you code it, it changes to H.  
44:46 Jim Liu: Okay.  
44:47 Reid, Rebekah: There's an ecoi that I know Dylan's been working on. I'm not sure if it's in the loop or not, but he added a column that says post coding. Not entirely sure, but  
45:05 Lukie Lu: Okay, I understand. This is the new widget.  
45:11 Reid, Rebekah: Yes. If you scroll down to the table, there you go right there. It mentions shown on assembly and P/E drawings. It indicates the age.  
45:11 Jim Liu: Okay, for sure after.  
45:11 Lukie Lu: So, okay.  
45:16 Jim Liu: Yes. If the dimension is a frog coated or uncoated, then it will make sense, right?  
45:24 Lukie Lu: Okay.  
45:33 Jim Liu: The diameter number is not exactly the same due to the coating. Okay, yes.  
45:44 Lukie Lu: That makes sense. Yes, I will inform the customer. Yes, this is the final diameter with coding. Thank you, everyone. That's all I have.  
46:01 Jim Liu: Okay. I forgot to ask Kevin. Hello, Kevin. Do we have any topics for disk inflator or any topics you want to discuss?  
46:14 Kevin Lu: Just one question for send for checks with. You're my sweetheart. It's for PDU and wife check this. The genetic weight is 34.4 and.  
46:59 Benthall, Sam E: Which one is this on the screen?  
47:00 Kevin Lu: There are beer joints.  
47:03 Benthall, Sam E: Which one?  
47:07 Kevin Lu: You missed it cold.  
47:10 Benthall, Sam E: Yes.  
47:12 Kevin Lu: USH 06.  
47:16 Benthall, Sam E: Okay.  
47:17 Kevin Lu: SH 06. It's for with W at the last support for wife.  
47:27 Benthall, Sam E: Okay. And the other one is SAH 30.  
47:30 Kevin Lu: Oh, no, no, it's the same. It's a PH OE, and this one is a listed join.  
47:41 Benthall, Sam E: Which one is released?  
47:45 Kevin Lu: As well 4454.4.  
47:50 Benthall, Sam E: Okay.  
47:50 Kevin Lu: So there is the joining, and this one is for 54.0.4 less. It's a pennsylier join. It's a pennsylier join and say ask us join and say ask US why.  
48:06 Benthall, Sam E: 'Cause it's different.  
48:07 Kevin Lu: So wait, change it? Dunno. Which is.  
48:14 Benthall, Sam E: I don't know.  
48:14 Kevin Lu: Which is a.  
48:15 Benthall, Sam E: It's well, it's whatever's on the release drawing.  
48:19 Kevin Lu: Yeah, I say that we can use the joint weight but want to know why. Maybe say yours as a BA join. Or sell assistance is. So if we tell Sims a reason.  
48:36 Benthall, Sam E: I don't.  
48:39 Kevin Lu: I concern, I concern, say say. Let us explain the reason.  
48:46 Benthall, Sam E: Yeah, we can check. It's maybe just the amount of booster change. I'm not sure.  
48:58 Kevin Lu: Oh, okay. So maybe tomorrow you can check and send an email to me.  
49:02 Benthall, Sam E: I probably can't check tomorrow, but if you'll send me an email eventually I can check.  
49:10 Kevin Lu: Okay.  
49:12 Benthall, Sam E: I will not be working tomorrow.  
49:15 Kevin Lu: Oh, okay. Oh yes, it's the questions, but.  
49:27 Benthall, Sam E: I'll check but I can't do it tomorrow.  
49:31 Kevin Lu: I understand, but does it want me to reply to send today? I will check how to reply.  
49:38 Benthall, Sam E: Tell them they don't need to know the reason. It's proprietary.  
49:42 Kevin Lu: Okay. Okay. Yes, we'll try to reply. Yes, we'll try to send an email also.  
49:56 Benthall, Sam E: Okay. This other meeting is starting, so.  
49:59 Super Guo: Yes, yes, yes. Let's move to another meeting. Yes, yes, yes, let's move to. Yes, yes, yes, let's move to another meeting, okay.  
50:00 Benthall, Sam E: Yes. Okay. Okay. Thanks.  
50:02 Jim Liu: Yes, yes, I think so.  
50:02 Super Guo: Okay. That's all, goodbye. That's all. Goodbye. Bye-bye.  
50:05 Kevin Lu: Okay.  
50:05 Jim Liu: Yes. Bye. Bye. No.  
50:06 Barry Wang: Now that's what I mean.
  